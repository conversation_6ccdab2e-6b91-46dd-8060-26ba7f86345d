# دليل البدء السريع - نظام إدارة شؤون الموظفين المتقدم

## 🚀 التثبيت والتشغيل السريع

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تهيئة البيانات الأولية
```bash
python init_data.py
```

### 3. تشغيل النظام
```bash
python run.py
```

### 4. الوصول للنظام
- افتح المتصفح على: `http://localhost:5000`
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## 🎯 الميزات الجديدة المضافة

### ⭐ نظام التقييم الوظيفي
- **الوصول**: لوحة التحكم → التقييم الوظيفي
- **الميزات**:
  - تقييم الأداء السنوي بمعايير متعددة
  - تتبع تطور الأداء عبر الزمن
  - تقارير تقييم شاملة
  - نظام الموافقات المتدرج

### 🎯 إدارة الأهداف والمؤشرات
- **الوصول**: لوحة التحكم → الأهداف / مؤشرات الأداء
- **الميزات**:
  - تحديد أهداف قابلة للقياس
  - متابعة مؤشرات الأداء الرئيسية (KPIs)
  - تتبع التقدم بصرياً
  - تحليل الإنجازات

### 🔔 نظام الإشعارات الذكي
- **الوصول**: لوحة التحكم → الإشعارات
- **الميزات**:
  - إشعارات انتهاء الإجازات
  - تذكيرات التقييم
  - تنبيهات المواعيد المهمة
  - إشعارات في الوقت الفعلي

### 🗄️ نظام الأرشفة المتقدم
- **الوصول**: لوحة التحكم → الأرشيف والنسخ الاحتياطية
- **الميزات**:
  - أرشفة تلقائية للبيانات القديمة
  - نسخ احتياطية مجدولة
  - استرداد البيانات المؤرشفة
  - إدارة التخزين الذكية

### 📈 لوحة التحكم المتقدمة
- **الوصول**: لوحة التحكم → لوحة التحكم المتقدمة
- **الميزات**:
  - رسوم بيانية تفاعلية
  - إحصائيات في الوقت الفعلي
  - تحليلات متعمقة
  - مؤشرات أداء مرئية

### 🔐 نظام الصلاحيات المتدرج
- **الوصول**: لوحة التحكم → الأدوار والصلاحيات
- **الميزات**:
  - أدوار مخصصة للمستخدمين
  - صلاحيات متدرجة ودقيقة
  - سجل شامل للعمليات
  - أمان محسن

---

## 📊 الأدوار الافتراضية

### 1. مدير النظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: جميع الصلاحيات

### 2. مدير الموارد البشرية
- **اسم المستخدم**: `hr_manager`
- **كلمة المرور**: `hr123`
- **الصلاحيات**: إدارة الموظفين والتقييمات

---

## 🛠️ الاستخدام السريع

### إضافة تقييم أداء جديد
1. انتقل إلى **التقييم الوظيفي**
2. اضغط **إضافة تقييم**
3. اختر الموظف والمقيم
4. املأ معايير التقييم (1-5)
5. أضف التعليقات والملاحظات
6. احفظ أو أرسل للاعتماد

### تحديد هدف جديد
1. انتقل إلى **الأهداف**
2. اضغط **إضافة هدف**
3. حدد الموظف والعنوان
4. اختر التاريخ المستهدف والأولوية
5. تابع التقدم وحدث النسب

### إنشاء مؤشر أداء
1. انتقل إلى **مؤشرات الأداء**
2. اضغط **إضافة مؤشر**
3. حدد اسم المؤشر والقيمة المستهدفة
4. اختر وحدة القياس والفترة
5. تابع التحديثات الدورية

### مراجعة الإشعارات
1. انتقل إلى **الإشعارات**
2. راجع الإشعارات الجديدة
3. حدد الإشعارات كمقروءة
4. تابع التذكيرات المهمة

---

## 📈 لوحة التحكم المتقدمة

### الوصول السريع
- **الرابط المباشر**: `http://localhost:5000/advanced_dashboard`
- **من القائمة**: لوحة التحكم → لوحة التحكم المتقدمة

### الميزات الرئيسية
- **مقاييس في الوقت الفعلي**: تحديث تلقائي كل 30 ثانية
- **رسوم بيانية تفاعلية**: رسوم متطورة باستخدام Chart.js
- **إحصائيات شاملة**: تحليلات متعمقة للأداء
- **سجل الأنشطة**: متابعة العمليات الأخيرة

---

## 🔧 إعدادات متقدمة

### تخصيص الإشعارات
- **ملف التكوين**: `config.py`
- **فترة الفحص**: `NOTIFICATION_CHECK_INTERVAL_MINUTES`
- **تذكيرات الإجازات**: `LEAVE_EXPIRY_NOTIFICATION_DAYS`

### إعدادات الأرشفة
- **الأرشفة التلقائية**: `AUTO_ARCHIVE_ENABLED`
- **فترة الاحتفاظ**: `ARCHIVE_ATTENDANCE_AFTER_DAYS`
- **النسخ الاحتياطية**: `AUTO_BACKUP_ENABLED`

### تخصيص الواجهة
- **الألوان الأساسية**: `THEME_COLOR_PRIMARY`
- **الوضع المظلم**: `ENABLE_DARK_MODE`
- **اتجاه النص**: `ENABLE_RTL`

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm employees.db
python init_data.py
```

#### 2. مشاكل في المتطلبات
```bash
# إعادة تثبيت المتطلبات
pip install --upgrade -r requirements.txt
```

#### 3. مشاكل في الصلاحيات
```bash
# إعادة تهيئة الأدوار
python init_data.py
```

#### 4. مشاكل في الملفات الثابتة
- تأكد من وجود مجلدات: `static/css/` و `static/js/`
- تحقق من روابط Bootstrap و Chart.js

---

## 📞 الدعم والمساعدة

### الموارد المفيدة
- **الوثائق الكاملة**: `README.md`
- **ملف التكوين**: `config.py`
- **سجلات النظام**: `logs/employees_system.log`

### التواصل
- إنشاء Issue في GitHub للمشاكل التقنية
- مراجعة الكود للتحسينات المقترحة
- المساهمة في تطوير الميزات الجديدة

---

## 🎉 استمتع بالنظام المتقدم!

تم تطوير هذا النظام ليكون حلاً شاملاً ومتقدماً لإدارة شؤون الموظفين مع التركيز على:
- **الأداء والكفاءة**
- **سهولة الاستخدام**
- **الأمان والموثوقية**
- **التطوير المستمر**

**نتمنى لك تجربة ممتازة مع النظام الجديد! 🚀**
