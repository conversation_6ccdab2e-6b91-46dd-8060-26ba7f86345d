# نظام إدارة شؤون الموظفين المتقدم

نظام شامل ومتقدم لإدارة شؤون الموظفين باستخدام Flask مع ميزات متطورة للتقييم والمتابعة.

## الميزات الأساسية

### 📊 إدارة الموظفين
- إضافة وتعديل وحذف بيانات الموظفين
- إدارة الأقسام والمناصب
- رفع وإدارة وثائق الموظفين
- تتبع تاريخ التوظيف والمعلومات الشخصية

### ⏰ إدارة الحضور والانصراف
- تسجيل الحضور والمغادرة اليومية
- تتبع ساعات العمل
- تقارير الحضور المفصلة
- إحصائيات الغياب والتأخير

### 🏖️ إدارة الإجازات
- طلبات الإجازات المختلفة (سنوية، مرضية، عارضة)
- نظام الموافقات والرفض
- تتبع رصيد الإجازات
- تقارير الإجازات

## الميزات المتقدمة الجديدة

### ⭐ نظام التقييم الوظيفي
- **تقييم الأداء السنوي**: تقييمات شاملة للموظفين
- **معايير متعددة**: المهارات التقنية، التواصل، العمل الجماعي، القيادة
- **تتبع التطور**: مقارنة الأداء عبر الزمن
- **تقارير مفصلة**: تقارير أداء شاملة لكل موظف

### 🎯 إدارة الأهداف والمؤشرات
- **تحديد الأهداف**: أهداف فردية قابلة للقياس
- **مؤشرات الأداء الرئيسية (KPIs)**: متابعة المؤشرات المهمة
- **تتبع التقدم**: نسب إنجاز مرئية
- **تحليل الإنجازات**: تقارير تحليلية للأهداف

### 🔔 نظام الإشعارات الذكي
- **إشعارات انتهاء الإجازات**: تذكيرات تلقائية
- **تذكيرات التقييم**: إشعارات للتقييمات المستحقة
- **المواعيد المهمة**: تنبيهات للمواعيد الحرجة
- **إشعارات في الوقت الفعلي**: تحديثات فورية

### 🗄️ نظام الأرشفة المتقدم
- **أرشفة تلقائية**: أرشفة البيانات القديمة تلقائياً
- **النسخ الاحتياطية**: نسخ احتياطية مجدولة
- **استرداد البيانات**: إمكانية استعادة البيانات المؤرشفة
- **إدارة التخزين**: تنظيف البيانات القديمة

### 📈 لوحة التحكم المتقدمة
- **رسوم بيانية تفاعلية**: رسوم بيانية متطورة باستخدام Chart.js
- **إحصائيات في الوقت الفعلي**: تحديث تلقائي للبيانات
- **تحليلات شاملة**: تحليلات متعمقة للأداء
- **مؤشرات الأداء**: عرض مرئي للمؤشرات الرئيسية

### 🔐 نظام الصلاحيات المتقدم
- **أدوار متعددة**: أدوار مخصصة للمستخدمين
- **صلاحيات متدرجة**: تحكم دقيق في الصلاحيات
- **تسجيل العمليات**: سجل شامل لجميع العمليات
- **أمان محسن**: حماية متقدمة للبيانات

## التقنيات المستخدمة

- **Backend**: Flask (Python)
- **Database**: SQLAlchemy مع SQLite
- **Frontend**: Bootstrap 5, Chart.js
- **Authentication**: Flask-Login
- **Data Processing**: Pandas, NumPy
- **Reports**: Excel, PDF export
- **Real-time Updates**: AJAX, JSON APIs

## متطلبات التشغيل

- Python 3.8+
- Flask 2.3+
- SQLAlchemy 2.0+
- Bootstrap 5
- Chart.js 3+

## التثبيت والتشغيل

1. **تثبيت المتطلبات**:
```bash
pip install -r requirements.txt
```

2. **تشغيل التطبيق**:
```bash
python app.py
```

3. **الوصول للنظام**:
- افتح المتصفح على: `http://localhost:5000`
- اسم المستخدم: `admin`
- كلمة المرور: `admin`

## الاستخدام

### إدارة التقييمات
1. انتقل إلى "التقييم الوظيفي"
2. اختر "إضافة تقييم" لإنشاء تقييم جديد
3. املأ معايير التقييم والتعليقات
4. احفظ التقييم أو أرسله للاعتماد

### إدارة الأهداف
1. انتقل إلى "الأهداف"
2. اختر "إضافة هدف" لتحديد هدف جديد
3. حدد الموظف والتاريخ المستهدف
4. تابع التقدم وحدث النسب

### مراقبة الإشعارات
1. انتقل إلى "الإشعارات"
2. راجع الإشعارات الجديدة
3. حدد الإشعارات كمقروءة
4. تابع التذكيرات المهمة

### إدارة الأرشيف
1. انتقل إلى "الأرشيف والنسخ الاحتياطية"
2. أنشئ نسخ احتياطية يدوية أو تلقائية
3. أرشف البيانات القديمة
4. استعد البيانات عند الحاجة

## الميزات التقنية المتقدمة

### الأمان
- تشفير كلمات المرور
- جلسات آمنة
- تسجيل العمليات
- صلاحيات متدرجة

### الأداء
- تحديث البيانات في الوقت الفعلي
- تخزين مؤقت للاستعلامات
- ضغط البيانات
- تحسين الاستعلامات

### التكامل
- APIs للتكامل الخارجي
- تصدير متعدد الصيغ
- استيراد البيانات
- تزامن البيانات

## الدعم والصيانة

### النسخ الاحتياطية
- نسخ احتياطية تلقائية يومية
- إمكانية النسخ اليدوي
- ضغط البيانات لتوفير المساحة
- تنظيف النسخ القديمة تلقائياً

### المراقبة
- مراقبة الأداء في الوقت الفعلي
- تتبع استخدام النظام
- إنذارات الأخطاء
- سجلات مفصلة للعمليات

### التحديثات
- تحديثات تلقائية للبيانات
- إشعارات النظام
- تحسينات الأداء
- إضافة ميزات جديدة

## الهيكل التنظيمي

```
├── app.py                 # التطبيق الرئيسي
├── config.py             # إعدادات النظام
├── requirements.txt      # المتطلبات
├── templates/           # قوالب HTML
│   ├── dashboard.html
│   ├── advanced_dashboard.html
│   ├── manage_performance_reviews.html
│   ├── manage_goals.html
│   ├── manage_kpis.html
│   ├── notifications.html
│   ├── manage_archives.html
│   └── ...
├── static/             # الملفات الثابتة
│   ├── css/
│   │   └── advanced-styles.css
│   └── js/
│       └── advanced-features.js
├── uploads/            # الملفات المرفوعة
├── backups/           # النسخ الاحتياطية
└── logs/              # ملفات السجلات
```

## المساهمة

نرحب بالمساهمات لتحسين النظام:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير
- مراجعة الوثائق

---

**تم تطوير هذا النظام ليكون حلاً شاملاً ومتقدماً لإدارة شؤون الموظفين مع التركيز على الأداء والتطوير المهني.**
