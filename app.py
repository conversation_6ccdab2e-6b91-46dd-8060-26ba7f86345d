from flask import Flask, render_template, request, redirect, url_for, flash, get_flashed_messages, send_from_directory, session, send_file, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta # أضف هذا السطر
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
import os
import pandas as pd
import tempfile
from collections import defaultdict
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key_here' # يجب تغيير هذا المفتاح في بيئة الإنتاج
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///employees.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif'} # يمكنك تعديل الامتدادات المسموح بها
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# التأكد من وجود مجلد التحميل
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# تعريف نموذج المستخدم لـ Flask-Login
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)
    email = db.Column(db.String(120), unique=True)
    full_name = db.Column(db.String(100))
    role_id = db.Column(db.Integer, db.ForeignKey('role.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    role = db.relationship('Role', backref='users')

    def __repr__(self):
        return f'<User {self.username}>'

    def has_permission(self, permission):
        """التحقق من وجود صلاحية معينة للمستخدم"""
        if not self.role or not self.role.permissions:
            return False
        import json
        try:
            permissions = json.loads(self.role.permissions)
            return permission in permissions
        except:
            return False

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# تعريف نموذج الموظف
class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # البيانات الشخصية
    full_name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(20), unique=True, nullable=False)
    age = db.Column(db.Integer)
    birth_place_city = db.Column(db.String(50))
    birth_place_region = db.Column(db.String(50))
    birth_place_municipality = db.Column(db.String(50))
    date_of_birth = db.Column(db.String(20))
    gender = db.Column(db.String(10))
    nationality = db.Column(db.String(50))

    # البيانات الوظيفية
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    job_title = db.Column(db.String(100))
    appointment_type = db.Column(db.String(50)) # تعيين-عقد-ندب-تكليف
    appointment_date = db.Column(db.String(20))
    start_work_date = db.Column(db.String(20))
    current_grade = db.Column(db.String(20))
    allowances_count = db.Column(db.Integer)
    last_promotion_date = db.Column(db.String(20))
    years_of_service = db.Column(db.Integer)
    photo = db.Column(db.String(100)) # مسار الصورة
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True) # إضافة حقل department_id
    department = db.relationship('Department', backref='employees', foreign_keys=[department_id]) # علاقة عكسية لجلب الموظفين في القسم

    def __repr__(self):
        return f'<Employee {self.full_name}>'


# تعريف نموذج القسم
class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.String(255))
    creation_date = db.Column(db.DateTime, default=datetime.utcnow) # إضافة تاريخ الإنشاء
    manager_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=True) # إضافة مدير القسم
    manager = db.relationship('Employee', foreign_keys=[manager_id]) # علاقة لمدير القسم

    def __repr__(self):
        return f'<Department {self.name}>'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        # هنا يمكنك إضافة منطق التحقق من قاعدة البيانات
        if username == 'admin' and password == 'password': # مثال بسيط
            return redirect(url_for('dashboard')) # إعادة توجيه إلى لوحة التحكم
        else:
            return render_template('login.html', error='اسم المستخدم أو كلمة المرور غير صحيحة')
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('username', None) # يمكنك تعديل هذا بناءً على كيفية تخزين معلومات المستخدم في الجلسة
    flash('تم تسجيل الخروج بنجاح!', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

@app.route('/advanced_dashboard')
def advanced_dashboard():
    """لوحة التحكم المتقدمة مع الإحصائيات في الوقت الفعلي"""
    # حساب الإحصائيات الأساسية
    total_employees = Employee.query.count()
    total_departments = Department.query.count()
    total_reviews = PerformanceReview.query.count()
    total_goals = Goal.query.count()
    total_kpis = KPI.query.count()

    # إحصائيات الحضور اليوم
    today = datetime.now().date()
    present_today = Attendance.query.filter_by(date=today, status='حاضر').count()
    absent_today = Attendance.query.filter_by(date=today, status='غائب').count()

    # إحصائيات الإجازات
    pending_leaves = Leave.query.filter_by(status='Pending').count()
    approved_leaves = Leave.query.filter_by(status='Approved').count()

    # متوسط التقييمات
    avg_performance = db.session.query(db.func.avg(PerformanceReview.overall_rating)).scalar() or 0

    # الأهداف النشطة
    active_goals = Goal.query.filter_by(status='Active').count()
    completed_goals = Goal.query.filter_by(status='Completed').count()

    # الإشعارات غير المقروءة
    unread_notifications = Notification.query.filter_by(is_read=False).count()

    stats = {
        'total_employees': total_employees,
        'total_departments': total_departments,
        'total_reviews': total_reviews,
        'total_goals': total_goals,
        'total_kpis': total_kpis,
        'present_today': present_today,
        'absent_today': absent_today,
        'pending_leaves': pending_leaves,
        'approved_leaves': approved_leaves,
        'avg_performance': round(avg_performance, 1),
        'active_goals': active_goals,
        'completed_goals': completed_goals,
        'unread_notifications': unread_notifications
    }

    return render_template('advanced_dashboard.html', stats=stats)

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    if request.method == 'POST':
        try:
            new_employee = Employee(
                full_name=request.form['full_name'],
                national_id=request.form['national_id'],
                age=request.form.get('age', type=int),
                birth_place_city=request.form.get('birth_place_city'),
                birth_place_region=request.form.get('birth_place_region'),
                birth_place_municipality=request.form.get('birth_place_municipality'),
                date_of_birth=request.form.get('date_of_birth'),
                gender=request.form.get('gender'),
                nationality=request.form.get('nationality'),
                employee_id=request.form['employee_id'],
                job_title=request.form.get('job_title'),
                appointment_type=request.form.get('appointment_type'),
                appointment_date=request.form.get('appointment_date'),
                start_work_date=request.form.get('start_work_date'),
                current_grade=request.form.get('current_grade'),
                allowances_count=request.form.get('allowances_count', type=int),
                last_promotion_date=request.form.get('last_promotion_date'),
                years_of_service=request.form.get('years_of_service', type=int),
                photo=request.form.get('photo')
            )
            db.session.add(new_employee)
            db.session.commit()
            flash('تمت إضافة الموظف بنجاح!', 'success')
            return redirect(url_for('view_employees')) # Redirect to view_employees after adding
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الموظف: {e}', 'danger')
            return redirect(url_for('add_employee')) # Redirect back to add_employee form
    return render_template('add_employee.html')

@app.route('/view_employees')
def view_employees():
    # الحصول على معلمات البحث من الاستعلام
    search_query = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'full_name')
    sort_order = request.args.get('sort_order', 'asc')
    
    # البدء بجميع الموظفين
    query = Employee.query
    
    # تطبيق البحث إذا تم تقديم استعلام
    if search_query:
        query = query.filter(
            db.or_(
                Employee.full_name.ilike(f'%{search_query}%'),
                Employee.national_id.ilike(f'%{search_query}%'),
                Employee.job_title.ilike(f'%{search_query}%')
            )
        )
    
    # تطبيق الترتيب
    if sort_order == 'asc':
        query = query.order_by(getattr(Employee, sort_by).asc())
    else:
        query = query.order_by(getattr(Employee, sort_by).desc())
    
    # الحصول على النتائج
    employees = query.all()
    
    return render_template('view_employees.html', employees=employees, 
                           search_query=search_query, sort_by=sort_by, sort_order=sort_order)

@app.route('/edit_employee/<int:employee_id>', methods=['GET', 'POST'])
def edit_employee(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    if request.method == 'POST':
        employee.full_name = request.form['full_name']
        employee.national_id = request.form['national_id']
        employee.age = request.form.get('age', type=int) # Added type=int
        employee.birth_place_city = request.form['birth_place_city']
        employee.birth_place_region = request.form['birth_place_region']
        employee.birth_place_municipality = request.form['birth_place_municipality']
        employee.date_of_birth = request.form['date_of_birth']
        employee.gender = request.form['gender']
        employee.nationality = request.form['nationality']
        employee.employee_id = request.form['employee_id']
        employee.job_title = request.form['job_title']
        employee.appointment_type = request.form['appointment_type']
        employee.appointment_date = request.form['appointment_date']
        employee.start_work_date = request.form['start_work_date']
        employee.current_grade = request.form['current_grade']
        employee.allowances_count = request.form.get('allowances_count', type=int) # Added type=int
        employee.last_promotion_date = request.form['last_promotion_date']
        employee.years_of_service = request.form.get('years_of_service', type=int) # Added type=int
        try:
            db.session.commit()
            flash('تم تحديث بيانات الموظف بنجاح!', 'success')
            return redirect(url_for('view_employees'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات الموظف: {e}', 'danger')
    return render_template('edit_employee.html', employee=employee)

@app.route('/delete_employee/<int:employee_id>', methods=['POST'])
def delete_employee(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    try:
        db.session.delete(employee)
        db.session.commit()
        flash('تم حذف الموظف بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الموظف: {e}', 'danger')
    return redirect(url_for('view_employees'))

@app.route('/employee_details/<int:employee_id>')
def employee_details(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    documents = Document.query.filter_by(employee_id=employee_id).all()
    return render_template('employee_details.html', employee=employee, documents=documents)

@app.route('/view_department/<int:department_id>')
def view_department(department_id):
    department = Department.query.get_or_404(department_id)
    employee_count = len(department.employees)
    manager_name = department.manager.full_name if department.manager else 'لا يوجد مدير'
    return render_template('department_details.html', department=department, employee_count=employee_count, manager_name=manager_name)

@app.route('/edit_department/<int:department_id>', methods=['GET', 'POST'])
def edit_department(department_id):
    department = Department.query.get_or_404(department_id)
    employees = Employee.query.all() # لجلب قائمة الموظفين لاختيار المدير
    if request.method == 'POST':
        department.name = request.form['name']
        department.description = request.form.get('description')
        manager_id = request.form.get('manager_id')
        if manager_id and manager_id != 'None':
            department.manager_id = int(manager_id)
        else:
            department.manager_id = None
        try:
            db.session.commit()
            flash('تم تحديث بيانات القسم بنجاح!', 'success')
            return redirect(url_for('manage_departments'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات القسم: {e}', 'danger')
    return render_template('edit_department.html', department=department, employees=employees)

@app.route('/delete_department/<int:department_id>', methods=['POST'])
def delete_department(department_id):
    department = Department.query.get_or_404(department_id)
    try:
        db.session.delete(department)
        db.session.commit()
        flash('تم حذف القسم بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف القسم: {e}', 'danger')
    return redirect(url_for('manage_departments'))

@app.route('/manage_departments')
def manage_departments():
    search_query = request.args.get('search', '')
    filter_manager_id = request.args.get('manager_id', type=int)

    departments_query = Department.query

    if search_query:
        departments_query = departments_query.filter(
            (Department.name.ilike(f'%{search_query}%')) |
            (Department.description.ilike(f'%{search_query}%'))
        )

    if filter_manager_id:
        departments_query = departments_query.filter_by(manager_id=filter_manager_id)

    departments = departments_query.all()

    departments_data = []
    for department in departments:
        employee_count = len(department.employees)
        manager_name = department.manager.full_name if department.manager else 'لا يوجد مدير'
        departments_data.append({
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'creation_date': department.creation_date.strftime('%Y-%m-%d %H:%M:%S'),
            'employee_count': employee_count,
            'manager_name': manager_name
        })
    
    all_employees = Employee.query.all() # لجلب قائمة الموظفين للفلترة حسب المدير

    return render_template('manage_departments.html', 
                           departments=departments_data, 
                           search_query=search_query, 
                           filter_manager_id=filter_manager_id,
                           all_employees=all_employees)

@app.route('/add_department', methods=['GET', 'POST'])
def add_department():
    employees = Employee.query.all() # لجلب قائمة الموظفين لاختيار المدير
    if request.method == 'POST':
        name = request.form['name']
        description = request.form.get('description')
        manager_id = request.form.get('manager_id')
        try:
            new_department = Department(name=name, description=description)
            if manager_id and manager_id != 'None':
                new_department.manager_id = int(manager_id)
            db.session.add(new_department)
            db.session.commit()
            flash('تمت إضافة القسم بنجاح!', 'success')
            return redirect(url_for('manage_departments'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة القسم: {e}', 'danger')
    return render_template('add_department.html', employees=employees)

# تعريف نموذج الحضور
class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(50), nullable=False) # مثلاً: حاضر, غائب, إجازة

    employee = db.relationship('Employee', backref='attendance')

    def __repr__(self):
        return f'<Attendance {self.employee_id} - {self.date} - {self.status}>'

@app.route('/manage_attendance')
def manage_attendance():
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_str = request.args.get('date', '')

    attendance_query = Attendance.query.join(Employee) # Join with Employee for searching by name

    if search_query:
        attendance_query = attendance_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        attendance_query = attendance_query.filter(Attendance.employee_id == filter_employee_id)

    if filter_date_str:
        try:
            filter_date = datetime.strptime(filter_date_str, '%Y-%m-%d').date()
            attendance_query = attendance_query.filter(Attendance.date == filter_date)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح. يرجى استخدام YYYY-MM-DD.', 'danger')

    attendance_records = attendance_query.all()
    all_employees = Employee.query.all() # لجلب قائمة الموظفين للفلترة

    return render_template('manage_attendance.html',
                           attendance_records=attendance_records,
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_date_str=filter_date_str,
                           all_employees=all_employees)

@app.route('/add_attendance', methods=['GET', 'POST'])
def add_attendance():
    employees = Employee.query.all()
    if request.method == 'POST':
        employee_id = request.form['employee_id']
        date_str = request.form['date']
        status = request.form['status']
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            new_attendance = Attendance(employee_id=employee_id, date=date_obj, status=status)
            db.session.add(new_attendance)
            db.session.commit()
            flash('تمت إضافة سجل الحضور بنجاح!', 'success')
            return redirect(url_for('manage_attendance'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة سجل الحضور: {e}', 'danger')
    return render_template('add_attendance.html', employees=employees)

@app.route('/edit_attendance/<int:attendance_id>', methods=['GET', 'POST'])
def edit_attendance(attendance_id):
    attendance = Attendance.query.get_or_404(attendance_id)
    employees = Employee.query.all()
    if request.method == 'POST':
        attendance.employee_id = request.form['employee_id']
        date_str = request.form['date']
        attendance.status = request.form['status']
        try:
            attendance.date = datetime.strptime(date_str, '%Y-%m-%d').date()
            db.session.commit()
            flash('تم تحديث سجل الحضور بنجاح!', 'success')
            return redirect(url_for('manage_attendance'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث سجل الحضور: {e}', 'danger')
    return render_template('edit_attendance.html', attendance=attendance, employees=employees)

@app.route('/delete_attendance/<int:attendance_id>', methods=['POST'])
def delete_attendance(attendance_id):
    attendance = Attendance.query.get_or_404(attendance_id)
    try:
        db.session.delete(attendance)
        db.session.commit()
        flash('تم حذف سجل الحضور بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف سجل الحضور: {e}', 'danger')
    return redirect(url_for('manage_attendance'))

# تعريف نموذج المغادرة
class Departure(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    departure_time = db.Column(db.Time, nullable=False) # وقت المغادرة

    employee = db.relationship('Employee', backref='departure')

    def __repr__(self):
        return f'<Departure {self.employee_id} - {self.date} - {self.departure_time}>'

# تعريف نموذج الوثائق
class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    document_type = db.Column(db.String(100), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    employee = db.relationship('Employee', backref='documents')

    def __repr__(self):
        return f'<Document {self.document_type} for Employee {self.employee_id}>'

# تعريف نموذج الإجازات
class Leave(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type = db.Column(db.String(50), nullable=False) # مثل: سنوية، مرضية، عارضة
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='Pending') # مثل: Pending, Approved, Rejected
    request_date = db.Column(db.DateTime, default=datetime.utcnow)
    employee = db.relationship('Employee', backref='leaves')

    def __repr__(self):
        return f'<Leave {self.leave_type} for Employee {self.employee_id}>'

# تعريف نموذج التقييم الوظيفي
class PerformanceReview(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    reviewer_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    review_period_start = db.Column(db.Date, nullable=False)
    review_period_end = db.Column(db.Date, nullable=False)
    overall_rating = db.Column(db.Float, nullable=False) # من 1 إلى 5
    technical_skills = db.Column(db.Float, nullable=False)
    communication_skills = db.Column(db.Float, nullable=False)
    teamwork = db.Column(db.Float, nullable=False)
    leadership = db.Column(db.Float, nullable=False)
    punctuality = db.Column(db.Float, nullable=False)
    initiative = db.Column(db.Float, nullable=False)
    comments = db.Column(db.Text)
    goals_achieved = db.Column(db.Text) # الأهداف المحققة
    areas_for_improvement = db.Column(db.Text) # مجالات التحسين
    future_goals = db.Column(db.Text) # الأهداف المستقبلية
    status = db.Column(db.String(20), default='Draft') # Draft, Submitted, Approved
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    submitted_date = db.Column(db.DateTime)
    approved_date = db.Column(db.DateTime)

    employee = db.relationship('Employee', foreign_keys=[employee_id], backref='performance_reviews')
    reviewer = db.relationship('Employee', foreign_keys=[reviewer_id], backref='reviews_conducted')

    def __repr__(self):
        return f'<PerformanceReview {self.employee_id} - {self.review_period_start} to {self.review_period_end}>'

# تعريف نموذج الأهداف
class Goal(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    target_date = db.Column(db.Date, nullable=False)
    priority = db.Column(db.String(20), default='Medium') # High, Medium, Low
    status = db.Column(db.String(20), default='Active') # Active, Completed, Cancelled
    progress_percentage = db.Column(db.Integer, default=0)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    completed_date = db.Column(db.DateTime)
    notes = db.Column(db.Text)

    employee = db.relationship('Employee', backref='goals')

    def __repr__(self):
        return f'<Goal {self.title} for Employee {self.employee_id}>'

# تعريف نموذج المؤشرات
class KPI(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    kpi_name = db.Column(db.String(200), nullable=False)
    target_value = db.Column(db.Float, nullable=False)
    current_value = db.Column(db.Float, default=0)
    unit = db.Column(db.String(50)) # وحدة القياس
    measurement_period = db.Column(db.String(50)) # شهري، ربع سنوي، سنوي
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)

    employee = db.relationship('Employee', backref='kpis')

    def __repr__(self):
        return f'<KPI {self.kpi_name} for Employee {self.employee_id}>'

# تعريف نموذج الإشعارات
class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False) # leave_expiry, review_reminder, important_date
    is_read = db.Column(db.Boolean, default=False)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    scheduled_date = db.Column(db.DateTime) # للإشعارات المجدولة
    related_id = db.Column(db.Integer) # معرف العنصر المرتبط (موظف، إجازة، إلخ)

    user = db.relationship('User', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.title} for User {self.user_id}>'

# تعريف نموذج الأدوار
class Role(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(255))
    permissions = db.Column(db.Text) # JSON string للصلاحيات
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Role {self.name}>'

# تعريف نموذج سجل العمليات
class ActivityLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False) # نوع العملية
    table_name = db.Column(db.String(50)) # اسم الجدول المتأثر
    record_id = db.Column(db.Integer) # معرف السجل المتأثر
    old_values = db.Column(db.Text) # القيم القديمة (JSON)
    new_values = db.Column(db.Text) # القيم الجديدة (JSON)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(255))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='activity_logs')

    def __repr__(self):
        return f'<ActivityLog {self.action} by User {self.user_id}>'

# تعريف نموذج الأرشيف
class Archive(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_name = db.Column(db.String(50), nullable=False)
    original_id = db.Column(db.Integer, nullable=False)
    data = db.Column(db.Text, nullable=False) # البيانات المؤرشفة (JSON)
    archived_date = db.Column(db.DateTime, default=datetime.utcnow)
    archived_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    reason = db.Column(db.String(255)) # سبب الأرشفة

    archived_by_user = db.relationship('User', backref='archived_records')

    def __repr__(self):
        return f'<Archive {self.table_name} ID:{self.original_id}>'

# تعريف نموذج النسخ الاحتياطية
class Backup(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    backup_name = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    backup_type = db.Column(db.String(50), nullable=False) # manual, automatic
    backup_size = db.Column(db.BigInteger) # حجم النسخة الاحتياطية بالبايت
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.String(20), default='Completed') # Completed, Failed, In Progress

    created_by_user = db.relationship('User', backref='backups_created')

    def __repr__(self):
        return f'<Backup {self.backup_name}>'

# تعريف نموذج المشاريع
class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='Active') # Active, Completed, On Hold, Cancelled
    priority = db.Column(db.String(20), default='Medium') # High, Medium, Low
    budget = db.Column(db.Float)
    progress_percentage = db.Column(db.Integer, default=0)
    manager_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    manager = db.relationship('Employee', foreign_keys=[manager_id], backref='managed_projects')
    department = db.relationship('Department', backref='projects')

    def __repr__(self):
        return f'<Project {self.name}>'

# تعريف نموذج المهام
class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=True)
    assigned_to = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    priority = db.Column(db.String(20), default='Medium') # High, Medium, Low
    status = db.Column(db.String(20), default='To Do') # To Do, In Progress, Review, Done
    due_date = db.Column(db.Date)
    estimated_hours = db.Column(db.Float)
    actual_hours = db.Column(db.Float)
    progress_percentage = db.Column(db.Integer, default=0)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    completed_date = db.Column(db.DateTime)

    project = db.relationship('Project', backref='tasks')
    assignee = db.relationship('Employee', foreign_keys=[assigned_to], backref='assigned_tasks')
    creator = db.relationship('Employee', foreign_keys=[created_by], backref='created_tasks')

    def __repr__(self):
        return f'<Task {self.title}>'

# تعريف نموذج الرواتب
class Salary(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    basic_salary = db.Column(db.Float, nullable=False)
    allowances = db.Column(db.Float, default=0) # البدلات
    overtime_hours = db.Column(db.Float, default=0)
    overtime_rate = db.Column(db.Float, default=0)
    deductions = db.Column(db.Float, default=0) # الخصومات
    bonus = db.Column(db.Float, default=0) # المكافآت
    net_salary = db.Column(db.Float, nullable=False)
    pay_period_start = db.Column(db.Date, nullable=False)
    pay_period_end = db.Column(db.Date, nullable=False)
    payment_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='Pending') # Pending, Paid, Cancelled
    notes = db.Column(db.Text)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    employee = db.relationship('Employee', backref='salaries')

    def __repr__(self):
        return f'<Salary {self.employee_id} - {self.pay_period_start} to {self.pay_period_end}>'

# تعريف نموذج التدريب
class Training(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    trainer_name = db.Column(db.String(100))
    training_type = db.Column(db.String(50)) # Internal, External, Online
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    duration_hours = db.Column(db.Integer)
    max_participants = db.Column(db.Integer)
    cost = db.Column(db.Float)
    location = db.Column(db.String(200))
    status = db.Column(db.String(20), default='Planned') # Planned, Ongoing, Completed, Cancelled
    created_date = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Training {self.title}>'

# تعريف نموذج التسجيل في التدريب
class TrainingEnrollment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    training_id = db.Column(db.Integer, db.ForeignKey('training.id'), nullable=False)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    enrollment_date = db.Column(db.DateTime, default=datetime.utcnow)
    attendance_status = db.Column(db.String(20), default='Enrolled') # Enrolled, Attended, Absent, Completed
    completion_score = db.Column(db.Float) # درجة الإكمال
    certificate_issued = db.Column(db.Boolean, default=False)
    feedback = db.Column(db.Text)

    training = db.relationship('Training', backref='enrollments')
    employee = db.relationship('Employee', backref='training_enrollments')

    def __repr__(self):
        return f'<TrainingEnrollment {self.employee_id} - {self.training_id}>'

# تعريف نموذج سجل الأنشطة المحسن
class ActivityLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    table_name = db.Column(db.String(50))
    record_id = db.Column(db.Integer)
    old_values = db.Column(db.Text) # القيم القديمة (JSON)
    new_values = db.Column(db.Text) # القيم الجديدة (JSON)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='activity_logs')

    def __repr__(self):
        return f'<ActivityLog {self.action} by {self.user_id}>'


@app.route('/manage_departure')
def manage_departure():
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_str = request.args.get('date', '')

    departure_query = Departure.query.join(Employee)

    if search_query:
        departure_query = departure_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        departure_query = departure_query.filter(Departure.employee_id == filter_employee_id)

    if filter_date_str:
        try:
            filter_date = datetime.strptime(filter_date_str, '%Y-%m-%d').date()
            departure_query = departure_query.filter(Departure.date == filter_date)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح. يرجى استخدام YYYY-MM-DD.', 'danger')

    departure_records = departure_query.all()
    all_employees = Employee.query.all()

    return render_template('manage_departure.html',
                           departure_records=departure_records,
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_date_str=filter_date_str,
                           all_employees=all_employees)

@app.route('/add_departure', methods=['GET', 'POST'])
def add_departure():
    employees = Employee.query.all()
    if request.method == 'POST':
        employee_id = request.form['employee_id']
        date_str = request.form['date']
        departure_time_str = request.form['departure_time']
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            departure_time_obj = datetime.strptime(departure_time_str, '%H:%M').time()
            new_departure = Departure(employee_id=employee_id, date=date_obj, departure_time=departure_time_obj)
            db.session.add(new_departure)
            db.session.commit()
            flash('تمت إضافة سجل المغادرة بنجاح!', 'success')
            return redirect(url_for('manage_departure'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة سجل المغادرة: {e}', 'danger')
    return render_template('add_departure.html', employees=employees)

@app.route('/edit_departure/<int:departure_id>', methods=['GET', 'POST'])
def edit_departure(departure_id):
    departure = Departure.query.get_or_404(departure_id)
    employees = Employee.query.all()
    if request.method == 'POST':
        departure.employee_id = request.form['employee_id']
        date_str = request.form['date']
        departure_time_str = request.form['departure_time']
        try:
            departure.date = datetime.strptime(date_str, '%Y-%m-%d').date()
            departure.departure_time = datetime.strptime(departure_time_str, '%H:%M').time()
            db.session.commit()
            flash('تم تحديث سجل المغادرة بنجاح!', 'success')
            return redirect(url_for('manage_departure'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث سجل المغادرة: {e}', 'danger')
    return render_template('edit_departure.html', departure=departure, employees=employees)

@app.route('/delete_departure/<int:departure_id>', methods=['POST'])
def delete_departure(departure_id):
    departure = Departure.query.get_or_404(departure_id)
    try:
        db.session.delete(departure)
        db.session.commit()
        flash('تم حذف سجل المغادرة بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف سجل المغادرة: {e}', 'danger')
    return redirect(url_for('manage_departure'))

# مسارات إدارة الوثائق
@app.route('/manage_documents')
def manage_documents():
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_document_type = request.args.get('document_type', '')

    documents_query = Document.query.join(Employee)

    if search_query:
        documents_query = documents_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        documents_query = documents_query.filter(Document.employee_id == filter_employee_id)

    if filter_document_type:
        documents_query = documents_query.filter(Document.document_type.ilike(f'%{filter_document_type}%'))

    documents = documents_query.all()
    all_employees = Employee.query.all()

    return render_template('manage_documents.html',
                           documents=documents,
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_document_type=filter_document_type,
                           all_employees=all_employees)

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/add_document', methods=['GET', 'POST'])
@app.route('/add_document/<int:employee_id>', methods=['GET', 'POST'])
def add_document(employee_id=None):
    employees = Employee.query.all()
    selected_employee = None
    
    if employee_id:
        selected_employee = Employee.query.get_or_404(employee_id)
    
    if request.method == 'POST':
        employee_id = request.form['employee_id']
        document_type = request.form['document_type']
        
        # Get all uploaded files
        uploaded_files = request.files.getlist('document_file')

        if not uploaded_files or all(f.filename == '' for f in uploaded_files):
            flash('الرجاء اختيار ملف واحد على الأقل للتحميل.', 'danger')
            return redirect(url_for('add_document'))

        for file in uploaded_files:
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                try:
                    file.save(file_path)
                    new_document = Document(employee_id=employee_id, document_type=document_type, file_path=filename) # Save only filename
                    db.session.add(new_document)
                    db.session.commit()
                    flash(f'تمت إضافة الوثيقة {filename} بنجاح!', 'success')
                except Exception as e:
                    db.session.rollback()
                    flash(f'حدث خطأ أثناء إضافة الوثيقة {filename}: {e}', 'danger')
            else:
                flash(f'الملف {file.filename} غير مسموح به أو لا يحتوي على اسم ملف.', 'danger')
        
        return redirect(url_for('manage_documents'))
    return render_template('add_document.html', employees=employees, selected_employee=selected_employee)

@app.route('/edit_document/<int:document_id>', methods=['GET', 'POST'])
def edit_document(document_id):
    document = Document.query.get_or_404(document_id)
    employees = Employee.query.all()
    if request.method == 'POST':
        document.employee_id = request.form['employee_id']
        document.document_type = request.form['document_type']
        
        # تحقق مما إذا تم تحميل ملف جديد
        if 'document_file' in request.files and request.files['document_file'].filename != '':
            file = request.files['document_file']
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                try:
                    file.save(file_path)
                    document.file_path = filename  # حفظ اسم الملف فقط
                except Exception as e:
                    flash(f'حدث خطأ أثناء تحميل الملف: {e}', 'danger')
                    return render_template('edit_document.html', document=document, employees=employees)
            else:
                flash('نوع الملف غير مسموح به.', 'danger')
                return render_template('edit_document.html', document=document, employees=employees)
        
        try:
            db.session.commit()
            flash('تم تحديث الوثيقة بنجاح!', 'success')
            return redirect(url_for('manage_documents'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الوثيقة: {e}', 'danger')
    return render_template('edit_document.html', document=document, employees=employees)

@app.route('/delete_document/<int:document_id>', methods=['POST'])
def delete_document(document_id):
    document = Document.query.get_or_404(document_id)
    try:
        db.session.delete(document)
        db.session.commit()
        flash('تم حذف الوثيقة بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الوثيقة: {e}', 'danger')
    return redirect(url_for('manage_documents'))

@app.route('/manage_leaves')
def manage_leaves():
    # استخراج معلمات البحث والتصفية
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status', '')
    filter_date_from = request.args.get('date_from', '')
    filter_date_to = request.args.get('date_to', '')
    
    # بناء استعلام الإجازات مع الانضمام إلى جدول الموظفين
    leaves_query = Leave.query.join(Employee)
    
    # تطبيق معايير البحث والتصفية
    if search_query:
        leaves_query = leaves_query.filter(Employee.full_name.like(f'%{search_query}%'))
    
    if filter_employee_id:
        leaves_query = leaves_query.filter(Leave.employee_id == filter_employee_id)
    
    if filter_status:
        leaves_query = leaves_query.filter(Leave.status == filter_status)
    
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        leaves_query = leaves_query.filter(Leave.start_date >= date_from)
    
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        leaves_query = leaves_query.filter(Leave.end_date <= date_to)
    
    # تنفيذ الاستعلام وجلب النتائج
    leaves = leaves_query.all()
    employees = Employee.query.all()  # جلب جميع الموظفين للقائمة المنسدلة

    # حساب إحصائيات الإجازات
    total_leaves = Leave.query.count()
    pending_leaves = Leave.query.filter_by(status='Pending').count()
    approved_leaves = Leave.query.filter_by(status='Approved').count()
    rejected_leaves = Leave.query.filter_by(status='Rejected').count()
    
    return render_template('manage_leaves.html', 
                           leaves=leaves, 
                           employees=employees, 
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_status=filter_status,
                           filter_date_from=filter_date_from,
                           filter_date_to=filter_date_to,
                           total_leaves=total_leaves,
                           pending_leaves=pending_leaves,
                           approved_leaves=approved_leaves,
                           rejected_leaves=rejected_leaves)

@app.route('/add_leave', methods=['GET', 'POST'])
def add_leave():
    employees = Employee.query.all()
    if request.method == 'POST':
        try:
            employee_id = request.form['employee_id']
            leave_type = request.form['leave_type']
            start_date_str = request.form['start_date']
            end_date_str = request.form['end_date']
            status = request.form['status']

            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

            # التحقق من صحة التواريخ
            if start_date > end_date:
                flash('تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء!', 'danger')
                return redirect(url_for('manage_leaves'))

            new_leave = Leave(employee_id=employee_id, leave_type=leave_type, 
                             start_date=start_date, end_date=end_date, status=status)
            db.session.add(new_leave)
            db.session.commit()
            flash('تمت إضافة طلب الإجازة بنجاح!', 'success')
            return redirect(url_for('manage_leaves'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة طلب الإجازة: {e}', 'danger')
            return redirect(url_for('manage_leaves'))
    return render_template('add_leave.html', employees=employees)

@app.route('/delete_leave/<int:leave_id>', methods=['POST'])
def delete_leave(leave_id):
    try:
        leave = Leave.query.get_or_404(leave_id)
        employee_name = leave.employee.full_name  # حفظ اسم الموظف قبل الحذف
        leave_type = leave.leave_type  # حفظ نوع الإجازة قبل الحذف
        db.session.delete(leave)
        db.session.commit()
        flash(f'تم حذف طلب الإجازة ({leave_type}) للموظف {employee_name} بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف طلب الإجازة: {e}', 'danger')
    return redirect(url_for('manage_leaves'))

@app.route('/edit_leave/<int:leave_id>', methods=['GET', 'POST'])
def edit_leave(leave_id):
    leave = Leave.query.get_or_404(leave_id)
    employees = Employee.query.all()
    
    if request.method == 'POST':
        leave.employee_id = request.form['employee_id']
        leave.leave_type = request.form['leave_type']
        start_date_str = request.form['start_date']
        end_date_str = request.form['end_date']
        leave.status = request.form['status']
        
        try:
            leave.start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            leave.end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            db.session.commit()
            flash('تم تحديث طلب الإجازة بنجاح!', 'success')
            return redirect(url_for('manage_leaves'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث طلب الإجازة: {e}', 'danger')
    
    return render_template('edit_leave.html', 
                         leave=leave, 
                         employees=employees,
                         leave_types=['سنوية', 'مرضية', 'عارضة'],
                         statuses=['Pending', 'Approved', 'Rejected'])

# مسار تقرير الحضور
@app.route('/attendance_report')
def attendance_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    
    # استعلام قاعدة البيانات للحصول على سجلات الحضور
    query = Attendance.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Attendance.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Attendance.status == filter_status)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Attendance.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Attendance.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # ترتيب النتائج حسب التاريخ تنازلياً
    attendance_records = query.order_by(Attendance.date.desc()).all()
    
    # حساب الإحصائيات
    stats = {
        'present': query.filter(Attendance.status == 'حاضر').count(),
        'absent': query.filter(Attendance.status == 'غائب').count(),
        'leave': query.filter(Attendance.status == 'إجازة').count(),
        'mission': query.filter(Attendance.status == 'مهمة رسمية').count()
    }
    
    # حساب إحصائيات الحضور الشهرية
    monthly_stats = calculate_monthly_attendance_stats(query)
    
    # إعداد ملخص الحضور حسب الموظف
    employee_summary = calculate_employee_attendance_summary(query)
    
    # الحصول على قوائم الموظفين والأقسام للتصفية
    all_employees = Employee.query.order_by(Employee.full_name).all()
    all_departments = Department.query.order_by(Department.name).all()
    
    return render_template('attendance_report.html',
                          attendance_records=attendance_records,
                          stats=stats,
                          monthly_stats=monthly_stats,
                          employee_summary=employee_summary,
                          all_employees=all_employees,
                          all_departments=all_departments,
                          filter_employee_id=filter_employee_id,
                          filter_status=filter_status,
                          filter_date_from=filter_date_from,
                          filter_date_to=filter_date_to,
                          filter_department_id=filter_department_id)

def calculate_monthly_attendance_stats(query):
    """حساب إحصائيات الحضور الشهرية"""
    # إنشاء قاموس لتخزين عدد أيام الحضور والغياب لكل شهر
    monthly_present = defaultdict(int)
    monthly_total = defaultdict(int)
    
    # الحصول على جميع سجلات الحضور
    all_records = query.all()
    
    # حساب عدد أيام الحضور والإجمالي لكل شهر
    for record in all_records:
        month = record.date.month - 1  # الفهرس يبدأ من 0
        monthly_total[month] += 1
        if record.status == 'حاضر':
            monthly_present[month] += 1
    
    # حساب نسبة الحضور لكل شهر
    monthly_rates = [0] * 12
    for month in range(12):
        if monthly_total[month] > 0:
            monthly_rates[month] = round((monthly_present[month] / monthly_total[month]) * 100)
    
    return monthly_rates

def calculate_employee_attendance_summary(query):
    """حساب ملخص الحضور لكل موظف"""
    # إنشاء قاموس لتخزين بيانات الحضور لكل موظف
    employee_data = defaultdict(lambda: {
        'present_days': 0,
        'absent_days': 0,
        'leave_days': 0,
        'mission_days': 0,
        'total_days': 0
    })
    
    # الحصول على جميع سجلات الحضور
    all_records = query.all()
    
    # حساب عدد أيام كل حالة لكل موظف
    for record in all_records:
        employee_id = record.employee_id
        employee_data[employee_id]['total_days'] += 1
        
        if record.status == 'حاضر':
            employee_data[employee_id]['present_days'] += 1
        elif record.status == 'غائب':
            employee_data[employee_id]['absent_days'] += 1
        elif record.status == 'إجازة':
            employee_data[employee_id]['leave_days'] += 1
        elif record.status == 'مهمة رسمية':
            employee_data[employee_id]['mission_days'] += 1
    
    # إعداد قائمة ملخص الحضور لكل موظف
    employee_summary = []
    for employee_id, data in employee_data.items():
        employee = Employee.query.get(employee_id)
        if employee:
            # حساب نسبة الحضور
            attendance_percentage = 0
            if data['total_days'] > 0:
                attendance_percentage = round((data['present_days'] / data['total_days']) * 100)
            
            employee_summary.append({
                'employee_name': employee.full_name,
                'employee_id': employee.employee_id,
                'department': employee.department.name if employee.department else 'غير محدد',
                'present_days': data['present_days'],
                'absent_days': data['absent_days'],
                'leave_days': data['leave_days'],
                'mission_days': data['mission_days'],
                'attendance_percentage': attendance_percentage
            })
    
    # ترتيب الملخص حسب نسبة الحضور تنازلياً
    employee_summary.sort(key=lambda x: x['attendance_percentage'], reverse=True)
    
    return employee_summary

# مسار تصدير تقرير الحضور
@app.route('/export_attendance_report')
def export_attendance_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    
    # استعلام قاعدة البيانات للحصول على سجلات الحضور
    query = Attendance.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Attendance.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Attendance.status == filter_status)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Attendance.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Attendance.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # ترتيب النتائج حسب التاريخ تنازلياً
    attendance_records = query.order_by(Attendance.date.desc()).all()
    
    # إنشاء DataFrame لتقرير الحضور
    data = []
    for record in attendance_records:
        data.append({
            'اسم الموظف': record.employee.full_name,
            'الرقم الوظيفي': record.employee.employee_id,
            'القسم': record.employee.department.name if record.employee.department else 'غير محدد',
            'التاريخ': record.date.strftime('%Y-%m-%d'),
            'الحالة': record.status,
            'ملاحظات': record.notes or ''
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مؤقت
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    # حفظ البيانات في ملف Excel
    df.to_excel(temp_filename, index=False, engine='openpyxl')
    
    # إرسال الملف للتنزيل
    return send_file(temp_filename, 
                     as_attachment=True,
                     download_name='تقرير_الحضور.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

# مسار تقرير المغادرة
@app.route('/departure_report')
def departure_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    filter_time_from = request.args.get('time_from')
    filter_time_to = request.args.get('time_to')
    
    # استعلام قاعدة البيانات للحصول على سجلات المغادرة
    query = Departure.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Departure.employee_id == filter_employee_id)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Departure.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Departure.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # تطبيق تصفية الوقت إذا تم تحديدها
    departure_records = query.order_by(Departure.date.desc()).all()
    if filter_time_from or filter_time_to:
        filtered_records = []
        for record in departure_records:
            time_str = record.departure_time.strftime('%H:%M')
            if filter_time_from and filter_time_to:
                if filter_time_from <= time_str <= filter_time_to:
                    filtered_records.append(record)
            elif filter_time_from:
                if filter_time_from <= time_str:
                    filtered_records.append(record)
            elif filter_time_to:
                if time_str <= filter_time_to:
                    filtered_records.append(record)
        departure_records = filtered_records
    
    # حساب الإحصائيات
    today = datetime.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    
    stats = {
        'today': sum(1 for r in departure_records if r.date == today),
        'week': sum(1 for r in departure_records if r.date >= week_start),
        'month': sum(1 for r in departure_records if r.date >= month_start),
        'total': len(departure_records)
    }
    
    # حساب إحصائيات المغادرة الشهرية
    monthly_stats = calculate_monthly_departure_stats(query)
    
    # إعداد بيانات الخريطة الحرارية
    heatmap_data = calculate_departure_heatmap_data(query)
    
    # إعداد ملخص المغادرة حسب الموظف
    employee_summary = calculate_employee_departure_summary(query)
    
    # الحصول على قوائم الموظفين والأقسام للتصفية
    all_employees = Employee.query.order_by(Employee.full_name).all()
    all_departments = Department.query.order_by(Department.name).all()
    
    return render_template('departure_report.html',
                          departure_records=departure_records,
                          stats=stats,
                          monthly_stats=monthly_stats,
                          heatmap_data=heatmap_data,
                          employee_summary=employee_summary,
                          all_employees=all_employees,
                          all_departments=all_departments,
                          filter_employee_id=filter_employee_id,
                          filter_date_from=filter_date_from,
                          filter_date_to=filter_date_to,
                          filter_department_id=filter_department_id,
                          filter_time_from=filter_time_from,
                          filter_time_to=filter_time_to)

def calculate_monthly_departure_stats(query):
    """حساب إحصائيات المغادرة الشهرية"""
    # إنشاء قاموس لتخزين عدد المغادرات لكل شهر
    monthly_departures = [0] * 12
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب عدد المغادرات لكل شهر
    for record in all_records:
        month = record.date.month - 1  # الفهرس يبدأ من 0
        monthly_departures[month] += 1
    
    return monthly_departures

def calculate_departure_heatmap_data(query):
    """إعداد بيانات الخريطة الحرارية لأوقات المغادرة"""
    # إنشاء مصفوفة لتخزين عدد المغادرات لكل يوم وساعة
    heatmap_data = [[0 for _ in range(8)] for _ in range(5)]  # 5 أيام × 8 ساعات
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب عدد المغادرات لكل يوم وساعة
    for record in all_records:
        weekday = record.date.weekday()
        if weekday < 5:  # فقط أيام الأسبوع (الأحد إلى الخميس)
            hour = record.departure_time.hour
            if 8 <= hour < 16:  # ساعات العمل (8 صباحاً إلى 4 مساءً)
                hour_index = hour - 8
                heatmap_data[weekday][hour_index] += 1
    
    return heatmap_data

def calculate_employee_departure_summary(query):
    """حساب ملخص المغادرة لكل موظف"""
    # إنشاء قاموس لتخزين بيانات المغادرة لكل موظف
    employee_data = defaultdict(lambda: {
        'departure_count': 0,
        'departure_times': [],
        'departure_days': defaultdict(int)
    })
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب بيانات المغادرة لكل موظف
    for record in all_records:
        employee_id = record.employee_id
        employee_data[employee_id]['departure_count'] += 1
        employee_data[employee_id]['departure_times'].append(record.departure_time)
        weekday = record.date.weekday()
        employee_data[employee_id]['departure_days'][weekday] += 1
    
    # إعداد قائمة ملخص المغادرة لكل موظف
    employee_summary = []
    weekday_names = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    
    for employee_id, data in employee_data.items():
        employee = Employee.query.get(employee_id)
        if employee:
            # حساب متوسط وقت المغادرة
            average_time = '00:00'
            earliest_time = '00:00'
            most_frequent_day = 'غير محدد'
            
            if data['departure_times']:
                # حساب متوسط الوقت
                total_seconds = sum(dt.hour * 3600 + dt.minute * 60 + dt.second for dt in data['departure_times'])
                avg_seconds = total_seconds // len(data['departure_times'])
                avg_hours, remainder = divmod(avg_seconds, 3600)
                avg_minutes, avg_seconds = divmod(remainder, 60)
                average_time = f'{avg_hours:02d}:{avg_minutes:02d}'
                
                # تحديد أبكر وقت مغادرة
                earliest = min(data['departure_times'], key=lambda x: x.hour * 60 + x.minute)
                earliest_time = earliest.strftime('%H:%M')
            
            # تحديد أكثر يوم للمغادرة
            if data['departure_days']:
                most_frequent_day_index = max(data['departure_days'], key=data['departure_days'].get)
                most_frequent_day = weekday_names[most_frequent_day_index]
            
            employee_summary.append({
                'employee_name': employee.full_name,
                'employee_id': employee.employee_id,
                'department': employee.department.name if employee.department else 'غير محدد',
                'departure_count': data['departure_count'],
                'average_time': average_time,
                'earliest_time': earliest_time,
                'most_frequent_day': most_frequent_day
            })
    
    # ترتيب الملخص حسب عدد المغادرات تنازلياً
    employee_summary.sort(key=lambda x: x['departure_count'], reverse=True)
    
    return employee_summary

# مسارات نظام التقييم الوظيفي
@app.route('/manage_performance_reviews')
def manage_performance_reviews():
    """عرض وإدارة تقييمات الأداء"""
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status', '')
    filter_year = request.args.get('year', type=int)

    reviews_query = PerformanceReview.query.join(Employee, PerformanceReview.employee_id == Employee.id)

    if search_query:
        reviews_query = reviews_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        reviews_query = reviews_query.filter(PerformanceReview.employee_id == filter_employee_id)

    if filter_status:
        reviews_query = reviews_query.filter(PerformanceReview.status == filter_status)

    if filter_year:
        reviews_query = reviews_query.filter(
            db.extract('year', PerformanceReview.review_period_start) == filter_year
        )

    reviews = reviews_query.order_by(PerformanceReview.created_date.desc()).all()
    all_employees = Employee.query.all()

    # حساب الإحصائيات
    total_reviews = PerformanceReview.query.count()
    draft_reviews = PerformanceReview.query.filter_by(status='Draft').count()
    submitted_reviews = PerformanceReview.query.filter_by(status='Submitted').count()
    approved_reviews = PerformanceReview.query.filter_by(status='Approved').count()

    return render_template('manage_performance_reviews.html',
                          reviews=reviews,
                          all_employees=all_employees,
                          search_query=search_query,
                          filter_employee_id=filter_employee_id,
                          filter_status=filter_status,
                          filter_year=filter_year,
                          total_reviews=total_reviews,
                          draft_reviews=draft_reviews,
                          submitted_reviews=submitted_reviews,
                          approved_reviews=approved_reviews)

@app.route('/add_performance_review', methods=['GET', 'POST'])
def add_performance_review():
    """إضافة تقييم أداء جديد"""
    employees = Employee.query.all()
    reviewers = Employee.query.all()

    if request.method == 'POST':
        try:
            new_review = PerformanceReview(
                employee_id=request.form['employee_id'],
                reviewer_id=request.form['reviewer_id'],
                review_period_start=datetime.strptime(request.form['review_period_start'], '%Y-%m-%d').date(),
                review_period_end=datetime.strptime(request.form['review_period_end'], '%Y-%m-%d').date(),
                overall_rating=float(request.form['overall_rating']),
                technical_skills=float(request.form['technical_skills']),
                communication_skills=float(request.form['communication_skills']),
                teamwork=float(request.form['teamwork']),
                leadership=float(request.form['leadership']),
                punctuality=float(request.form['punctuality']),
                initiative=float(request.form['initiative']),
                comments=request.form.get('comments'),
                goals_achieved=request.form.get('goals_achieved'),
                areas_for_improvement=request.form.get('areas_for_improvement'),
                future_goals=request.form.get('future_goals'),
                status=request.form.get('status', 'Draft')
            )

            if new_review.status == 'Submitted':
                new_review.submitted_date = datetime.utcnow()
            elif new_review.status == 'Approved':
                new_review.approved_date = datetime.utcnow()

            db.session.add(new_review)
            db.session.commit()
            flash('تمت إضافة تقييم الأداء بنجاح!', 'success')
            return redirect(url_for('manage_performance_reviews'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة تقييم الأداء: {e}', 'danger')

    return render_template('add_performance_review.html', employees=employees, reviewers=reviewers)

@app.route('/edit_performance_review/<int:review_id>', methods=['GET', 'POST'])
def edit_performance_review(review_id):
    """تعديل تقييم الأداء"""
    review = PerformanceReview.query.get_or_404(review_id)
    employees = Employee.query.all()
    reviewers = Employee.query.all()

    if request.method == 'POST':
        try:
            review.employee_id = request.form['employee_id']
            review.reviewer_id = request.form['reviewer_id']
            review.review_period_start = datetime.strptime(request.form['review_period_start'], '%Y-%m-%d').date()
            review.review_period_end = datetime.strptime(request.form['review_period_end'], '%Y-%m-%d').date()
            review.overall_rating = float(request.form['overall_rating'])
            review.technical_skills = float(request.form['technical_skills'])
            review.communication_skills = float(request.form['communication_skills'])
            review.teamwork = float(request.form['teamwork'])
            review.leadership = float(request.form['leadership'])
            review.punctuality = float(request.form['punctuality'])
            review.initiative = float(request.form['initiative'])
            review.comments = request.form.get('comments')
            review.goals_achieved = request.form.get('goals_achieved')
            review.areas_for_improvement = request.form.get('areas_for_improvement')
            review.future_goals = request.form.get('future_goals')

            old_status = review.status
            review.status = request.form.get('status', 'Draft')

            if old_status != 'Submitted' and review.status == 'Submitted':
                review.submitted_date = datetime.utcnow()
            elif old_status != 'Approved' and review.status == 'Approved':
                review.approved_date = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث تقييم الأداء بنجاح!', 'success')
            return redirect(url_for('manage_performance_reviews'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث تقييم الأداء: {e}', 'danger')

    return render_template('edit_performance_review.html', review=review, employees=employees, reviewers=reviewers)

@app.route('/delete_performance_review/<int:review_id>', methods=['POST'])
def delete_performance_review(review_id):
    """حذف تقييم الأداء"""
    review = PerformanceReview.query.get_or_404(review_id)
    try:
        db.session.delete(review)
        db.session.commit()
        flash('تم حذف تقييم الأداء بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف تقييم الأداء: {e}', 'danger')
    return redirect(url_for('manage_performance_reviews'))

# مسارات إدارة الأهداف
@app.route('/manage_goals')
def manage_goals():
    """عرض وإدارة الأهداف"""
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status', '')
    filter_priority = request.args.get('priority', '')

    goals_query = Goal.query.join(Employee)

    if search_query:
        goals_query = goals_query.filter(
            db.or_(
                Employee.full_name.ilike(f'%{search_query}%'),
                Goal.title.ilike(f'%{search_query}%')
            )
        )

    if filter_employee_id:
        goals_query = goals_query.filter(Goal.employee_id == filter_employee_id)

    if filter_status:
        goals_query = goals_query.filter(Goal.status == filter_status)

    if filter_priority:
        goals_query = goals_query.filter(Goal.priority == filter_priority)

    goals = goals_query.order_by(Goal.target_date.asc()).all()
    all_employees = Employee.query.all()

    return render_template('manage_goals.html',
                          goals=goals,
                          all_employees=all_employees,
                          search_query=search_query,
                          filter_employee_id=filter_employee_id,
                          filter_status=filter_status,
                          filter_priority=filter_priority)

@app.route('/add_goal', methods=['GET', 'POST'])
def add_goal():
    """إضافة هدف جديد"""
    employees = Employee.query.all()

    if request.method == 'POST':
        try:
            new_goal = Goal(
                employee_id=request.form['employee_id'],
                title=request.form['title'],
                description=request.form.get('description'),
                target_date=datetime.strptime(request.form['target_date'], '%Y-%m-%d').date(),
                priority=request.form.get('priority', 'Medium'),
                progress_percentage=int(request.form.get('progress_percentage', 0)),
                notes=request.form.get('notes')
            )

            db.session.add(new_goal)
            db.session.commit()
            flash('تمت إضافة الهدف بنجاح!', 'success')
            return redirect(url_for('manage_goals'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الهدف: {e}', 'danger')

    return render_template('add_goal.html', employees=employees)

@app.route('/edit_goal/<int:goal_id>', methods=['GET', 'POST'])
def edit_goal(goal_id):
    """تعديل الهدف"""
    goal = Goal.query.get_or_404(goal_id)
    employees = Employee.query.all()

    if request.method == 'POST':
        try:
            goal.employee_id = request.form['employee_id']
            goal.title = request.form['title']
            goal.description = request.form.get('description')
            goal.target_date = datetime.strptime(request.form['target_date'], '%Y-%m-%d').date()
            goal.priority = request.form.get('priority', 'Medium')
            goal.status = request.form.get('status', 'Active')
            goal.progress_percentage = int(request.form.get('progress_percentage', 0))
            goal.notes = request.form.get('notes')

            if goal.status == 'Completed' and goal.progress_percentage == 100:
                goal.completed_date = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث الهدف بنجاح!', 'success')
            return redirect(url_for('manage_goals'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الهدف: {e}', 'danger')

    return render_template('edit_goal.html', goal=goal, employees=employees)

@app.route('/delete_goal/<int:goal_id>', methods=['POST'])
def delete_goal(goal_id):
    """حذف الهدف"""
    goal = Goal.query.get_or_404(goal_id)
    try:
        db.session.delete(goal)
        db.session.commit()
        flash('تم حذف الهدف بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الهدف: {e}', 'danger')
    return redirect(url_for('manage_goals'))

# مسارات إدارة المؤشرات
@app.route('/manage_kpis')
def manage_kpis():
    """عرض وإدارة مؤشرات الأداء الرئيسية"""
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_period = request.args.get('period', '')

    kpis_query = KPI.query.join(Employee)

    if search_query:
        kpis_query = kpis_query.filter(
            db.or_(
                Employee.full_name.ilike(f'%{search_query}%'),
                KPI.kpi_name.ilike(f'%{search_query}%')
            )
        )

    if filter_employee_id:
        kpis_query = kpis_query.filter(KPI.employee_id == filter_employee_id)

    if filter_period:
        kpis_query = kpis_query.filter(KPI.measurement_period == filter_period)

    kpis = kpis_query.order_by(KPI.last_updated.desc()).all()
    all_employees = Employee.query.all()

    return render_template('manage_kpis.html',
                          kpis=kpis,
                          all_employees=all_employees,
                          search_query=search_query,
                          filter_employee_id=filter_employee_id,
                          filter_period=filter_period)

@app.route('/add_kpi', methods=['GET', 'POST'])
def add_kpi():
    """إضافة مؤشر أداء جديد"""
    employees = Employee.query.all()

    if request.method == 'POST':
        try:
            new_kpi = KPI(
                employee_id=request.form['employee_id'],
                kpi_name=request.form['kpi_name'],
                target_value=float(request.form['target_value']),
                current_value=float(request.form.get('current_value', 0)),
                unit=request.form.get('unit'),
                measurement_period=request.form.get('measurement_period', 'شهري')
            )

            db.session.add(new_kpi)
            db.session.commit()
            flash('تمت إضافة مؤشر الأداء بنجاح!', 'success')
            return redirect(url_for('manage_kpis'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة مؤشر الأداء: {e}', 'danger')

    return render_template('add_kpi.html', employees=employees)

@app.route('/edit_kpi/<int:kpi_id>', methods=['GET', 'POST'])
def edit_kpi(kpi_id):
    """تعديل مؤشر الأداء"""
    kpi = KPI.query.get_or_404(kpi_id)
    employees = Employee.query.all()

    if request.method == 'POST':
        try:
            kpi.employee_id = request.form['employee_id']
            kpi.kpi_name = request.form['kpi_name']
            kpi.target_value = float(request.form['target_value'])
            kpi.current_value = float(request.form.get('current_value', 0))
            kpi.unit = request.form.get('unit')
            kpi.measurement_period = request.form.get('measurement_period', 'شهري')
            kpi.last_updated = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث مؤشر الأداء بنجاح!', 'success')
            return redirect(url_for('manage_kpis'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث مؤشر الأداء: {e}', 'danger')

    return render_template('edit_kpi.html', kpi=kpi, employees=employees)

@app.route('/update_kpi/<int:kpi_id>', methods=['POST'])
def update_kpi(kpi_id):
    """تحديث قيمة مؤشر الأداء"""
    kpi = KPI.query.get_or_404(kpi_id)
    try:
        kpi.current_value = float(request.form['new_value'])
        kpi.last_updated = datetime.utcnow()
        db.session.commit()
        flash('تم تحديث قيمة المؤشر بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث المؤشر: {e}', 'danger')
    return redirect(url_for('manage_kpis'))

@app.route('/delete_kpi/<int:kpi_id>', methods=['POST'])
def delete_kpi(kpi_id):
    """حذف مؤشر الأداء"""
    kpi = KPI.query.get_or_404(kpi_id)
    try:
        db.session.delete(kpi)
        db.session.commit()
        flash('تم حذف مؤشر الأداء بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف مؤشر الأداء: {e}', 'danger')
    return redirect(url_for('manage_kpis'))

# مسار تصدير تقرير المغادرة
@app.route('/export_departure_report')
def export_departure_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    filter_time_from = request.args.get('time_from')
    filter_time_to = request.args.get('time_to')
    
    # استعلام قاعدة البيانات للحصول على سجلات المغادرة
    query = Departure.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Departure.employee_id == filter_employee_id)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Departure.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Departure.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # الحصول على سجلات المغادرة
    departure_records = query.order_by(Departure.date.desc()).all()
    
    # تطبيق تصفية الوقت إذا تم تحديدها
    if filter_time_from or filter_time_to:
        filtered_records = []
        for record in departure_records:
            time_str = record.departure_time.strftime('%H:%M')
            if filter_time_from and filter_time_to:
                if filter_time_from <= time_str <= filter_time_to:
                    filtered_records.append(record)
            elif filter_time_from:
                if filter_time_from <= time_str:
                    filtered_records.append(record)
            elif filter_time_to:
                if time_str <= filter_time_to:
                    filtered_records.append(record)
        departure_records = filtered_records
    
    # إنشاء DataFrame لتقرير المغادرة
    data = []
    for record in departure_records:
        data.append({
            'اسم الموظف': record.employee.full_name,
            'الرقم الوظيفي': record.employee.employee_id,
            'القسم': record.employee.department.name if record.employee.department else 'غير محدد',
            'التاريخ': record.date.strftime('%Y-%m-%d'),
            'وقت المغادرة': record.departure_time.strftime('%H:%M'),
            'سبب المغادرة': record.reason or ''
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مؤقت
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    # حفظ البيانات في ملف Excel
    df.to_excel(temp_filename, index=False, engine='openpyxl')
    
    # إرسال الملف للتنزيل
    return send_file(temp_filename, 
                     as_attachment=True,
                     download_name='تقرير_المغادرة.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

# مسارات نظام الإشعارات
@app.route('/notifications')
def view_notifications():
    """عرض الإشعارات"""
    user_id = 1  # يجب الحصول على معرف المستخدم الحالي
    notifications = Notification.query.filter_by(user_id=user_id).order_by(Notification.created_date.desc()).all()
    unread_count = Notification.query.filter_by(user_id=user_id, is_read=False).count()

    return render_template('notifications.html', notifications=notifications, unread_count=unread_count)

@app.route('/mark_notification_read/<int:notification_id>')
def mark_notification_read(notification_id):
    """تحديد الإشعار كمقروء"""
    notification = Notification.query.get_or_404(notification_id)
    notification.is_read = True
    db.session.commit()
    return redirect(url_for('view_notifications'))

@app.route('/create_notification', methods=['POST'])
def create_notification():
    """إنشاء إشعار جديد"""
    try:
        new_notification = Notification(
            user_id=request.form['user_id'],
            title=request.form['title'],
            message=request.form['message'],
            notification_type=request.form['notification_type'],
            related_id=request.form.get('related_id', type=int)
        )

        db.session.add(new_notification)
        db.session.commit()
        flash('تم إنشاء الإشعار بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء الإشعار: {e}', 'danger')

    return redirect(request.referrer or url_for('dashboard'))

# وظائف الإشعارات التلقائية
def check_leave_expiry_notifications():
    """فحص الإجازات التي تنتهي قريباً وإنشاء إشعارات"""
    from datetime import date, timedelta

    # البحث عن الإجازات التي تنتهي خلال 7 أيام
    end_date_threshold = date.today() + timedelta(days=7)
    expiring_leaves = Leave.query.filter(
        Leave.end_date <= end_date_threshold,
        Leave.end_date >= date.today(),
        Leave.status == 'Approved'
    ).all()

    for leave in expiring_leaves:
        # التحقق من عدم وجود إشعار مسبق لهذه الإجازة
        existing_notification = Notification.query.filter_by(
            notification_type='leave_expiry',
            related_id=leave.id
        ).first()

        if not existing_notification:
            days_left = (leave.end_date - date.today()).days
            notification = Notification(
                user_id=1,  # يجب تحديد المستخدم المناسب
                title=f'انتهاء إجازة قريباً',
                message=f'ستنتهي إجازة {leave.employee.full_name} ({leave.leave_type}) خلال {days_left} أيام',
                notification_type='leave_expiry',
                related_id=leave.id
            )
            db.session.add(notification)

    db.session.commit()

def check_review_reminders():
    """فحص التقييمات المستحقة وإنشاء تذكيرات"""
    from datetime import date, timedelta

    # البحث عن الموظفين الذين لم يتم تقييمهم هذا العام
    current_year = date.today().year
    employees_without_review = db.session.query(Employee).filter(
        ~Employee.id.in_(
            db.session.query(PerformanceReview.employee_id).filter(
                db.extract('year', PerformanceReview.review_period_start) == current_year
            )
        )
    ).all()

    for employee in employees_without_review:
        # التحقق من عدم وجود تذكير مسبق
        existing_notification = Notification.query.filter_by(
            notification_type='review_reminder',
            related_id=employee.id
        ).first()

        if not existing_notification:
            notification = Notification(
                user_id=1,  # يجب تحديد المستخدم المناسب
                title=f'تذكير تقييم أداء',
                message=f'يحتاج الموظف {employee.full_name} إلى تقييم أداء لهذا العام',
                notification_type='review_reminder',
                related_id=employee.id
            )
            db.session.add(notification)

    db.session.commit()

def check_goal_deadlines():
    """فحص الأهداف التي تقترب مواعيدها النهائية"""
    from datetime import date, timedelta

    # البحث عن الأهداف التي تنتهي خلال 7 أيام
    deadline_threshold = date.today() + timedelta(days=7)
    approaching_goals = Goal.query.filter(
        Goal.target_date <= deadline_threshold,
        Goal.target_date >= date.today(),
        Goal.status == 'Active'
    ).all()

    for goal in approaching_goals:
        # التحقق من عدم وجود إشعار مسبق
        existing_notification = Notification.query.filter_by(
            notification_type='goal_deadline',
            related_id=goal.id
        ).first()

        if not existing_notification:
            days_left = (goal.target_date - date.today()).days
            notification = Notification(
                user_id=1,  # يجب تحديد المستخدم المناسب
                title=f'موعد نهائي قريب للهدف',
                message=f'الهدف "{goal.title}" للموظف {goal.employee.full_name} ينتهي خلال {days_left} أيام',
                notification_type='goal_deadline',
                related_id=goal.id
            )
            db.session.add(notification)

    db.session.commit()

@app.route('/check_notifications')
def check_notifications():
    """فحص وإنشاء الإشعارات التلقائية"""
    try:
        check_leave_expiry_notifications()
        check_review_reminders()
        check_goal_deadlines()
        flash('تم فحص الإشعارات بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء فحص الإشعارات: {e}', 'danger')

    return redirect(url_for('view_notifications'))

# مسارات نظام الأرشفة
@app.route('/manage_archives')
def manage_archives():
    """عرض وإدارة الأرشيف"""
    search_query = request.args.get('search', '')
    filter_table = request.args.get('table', '')

    archives_query = Archive.query.join(User, Archive.archived_by == User.id)

    if search_query:
        archives_query = archives_query.filter(Archive.reason.ilike(f'%{search_query}%'))

    if filter_table:
        archives_query = archives_query.filter(Archive.table_name == filter_table)

    archives = archives_query.order_by(Archive.archived_date.desc()).all()

    return render_template('manage_archives.html', archives=archives,
                          search_query=search_query, filter_table=filter_table)

@app.route('/create_backup', methods=['POST'])
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        import sqlite3
        import shutil

        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.db'
        backup_path = os.path.join('backups', backup_filename)

        # التأكد من وجود مجلد النسخ الاحتياطية
        if not os.path.exists('backups'):
            os.makedirs('backups')

        # نسخ قاعدة البيانات
        shutil.copy2('employees.db', backup_path)

        # حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
        backup_size = os.path.getsize(backup_path)
        new_backup = Backup(
            backup_name=backup_filename,
            file_path=backup_path,
            backup_type='manual',
            backup_size=backup_size,
            created_by=1  # يجب الحصول على معرف المستخدم الحالي
        )

        db.session.add(new_backup)
        db.session.commit()
        flash('تم إنشاء النسخة الاحتياطية بنجاح!', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {e}', 'danger')

    return redirect(url_for('manage_archives'))

@app.route('/manage_backups')
def manage_backups():
    """عرض وإدارة النسخ الاحتياطية"""
    backups = Backup.query.order_by(Backup.created_date.desc()).all()
    return render_template('manage_backups.html', backups=backups)

@app.route('/archive_old_data')
def archive_old_data():
    """أرشفة البيانات القديمة"""
    try:
        import json
        from datetime import date, timedelta

        # أرشفة سجلات الحضور الأقدم من سنة
        cutoff_date = date.today() - timedelta(days=365)
        old_attendance = Attendance.query.filter(Attendance.date < cutoff_date).all()

        for record in old_attendance:
            # تحويل البيانات إلى JSON
            data = {
                'employee_id': record.employee_id,
                'date': record.date.isoformat(),
                'status': record.status
            }

            # إنشاء سجل أرشيف
            archive = Archive(
                table_name='Attendance',
                original_id=record.id,
                data=json.dumps(data, ensure_ascii=False),
                archived_by=1,  # يجب تحديد المستخدم الحالي
                reason='أرشفة تلقائية للبيانات الأقدم من سنة'
            )

            db.session.add(archive)
            db.session.delete(record)

        # أرشفة الإجازات المكتملة الأقدم من سنة
        old_leaves = Leave.query.filter(
            Leave.end_date < cutoff_date,
            Leave.status.in_(['Approved', 'Rejected'])
        ).all()

        for leave in old_leaves:
            data = {
                'employee_id': leave.employee_id,
                'leave_type': leave.leave_type,
                'start_date': leave.start_date.isoformat(),
                'end_date': leave.end_date.isoformat(),
                'status': leave.status,
                'request_date': leave.request_date.isoformat()
            }

            archive = Archive(
                table_name='Leave',
                original_id=leave.id,
                data=json.dumps(data, ensure_ascii=False),
                archived_by=1,
                reason='أرشفة تلقائية للإجازات المكتملة الأقدم من سنة'
            )

            db.session.add(archive)
            db.session.delete(leave)

        db.session.commit()
        flash(f'تم أرشفة {len(old_attendance)} سجل حضور و {len(old_leaves)} إجازة بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الأرشفة: {e}', 'danger')

    return redirect(url_for('manage_archives'))

@app.route('/restore_archive/<int:archive_id>', methods=['POST'])
def restore_archive(archive_id):
    """استعادة سجل من الأرشيف"""
    try:
        import json
        archive = Archive.query.get_or_404(archive_id)
        data = json.loads(archive.data)

        if archive.table_name == 'Attendance':
            restored_record = Attendance(
                employee_id=data['employee_id'],
                date=datetime.strptime(data['date'], '%Y-%m-%d').date(),
                status=data['status']
            )
            db.session.add(restored_record)

        elif archive.table_name == 'Leave':
            restored_record = Leave(
                employee_id=data['employee_id'],
                leave_type=data['leave_type'],
                start_date=datetime.strptime(data['start_date'], '%Y-%m-%d').date(),
                end_date=datetime.strptime(data['end_date'], '%Y-%m-%d').date(),
                status=data['status'],
                request_date=datetime.strptime(data['request_date'], '%Y-%m-%dT%H:%M:%S.%f')
            )
            db.session.add(restored_record)

        # حذف السجل من الأرشيف
        db.session.delete(archive)
        db.session.commit()

        flash('تم استعادة السجل بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استعادة السجل: {e}', 'danger')

    return redirect(url_for('manage_archives'))

@app.route('/delete_archive/<int:archive_id>')
def delete_archive(archive_id):
    """حذف سجل من الأرشيف نهائياً"""
    try:
        archive = Archive.query.get_or_404(archive_id)
        db.session.delete(archive)
        db.session.commit()
        flash('تم حذف السجل من الأرشيف نهائياً!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف السجل: {e}', 'danger')

    return redirect(url_for('manage_archives'))

@app.route('/clean_old_backups')
def clean_old_backups():
    """تنظيف النسخ الاحتياطية القديمة"""
    try:
        from datetime import timedelta

        # حذف النسخ الاحتياطية الأقدم من 30 يوماً
        cutoff_date = datetime.now() - timedelta(days=30)
        old_backups = Backup.query.filter(Backup.created_date < cutoff_date).all()

        deleted_count = 0
        for backup in old_backups:
            # حذف الملف الفعلي
            if os.path.exists(backup.file_path):
                os.remove(backup.file_path)

            # حذف السجل من قاعدة البيانات
            db.session.delete(backup)
            deleted_count += 1

        db.session.commit()
        flash(f'تم حذف {deleted_count} نسخة احتياطية قديمة!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تنظيف النسخ الاحتياطية: {e}', 'danger')

    return redirect(url_for('manage_backups'))

# مسارات نظام الصلاحيات
@app.route('/manage_roles')
def manage_roles():
    """عرض وإدارة الأدوار"""
    roles = Role.query.all()
    return render_template('manage_roles.html', roles=roles)

@app.route('/add_role', methods=['GET', 'POST'])
def add_role():
    """إضافة دور جديد"""
    if request.method == 'POST':
        try:
            import json

            # جمع الصلاحيات المحددة
            permissions = []
            if request.form.get('can_view_employees'): permissions.append('view_employees')
            if request.form.get('can_edit_employees'): permissions.append('edit_employees')
            if request.form.get('can_delete_employees'): permissions.append('delete_employees')
            if request.form.get('can_manage_departments'): permissions.append('manage_departments')
            if request.form.get('can_manage_attendance'): permissions.append('manage_attendance')
            if request.form.get('can_manage_leaves'): permissions.append('manage_leaves')
            if request.form.get('can_view_reports'): permissions.append('view_reports')
            if request.form.get('can_manage_performance'): permissions.append('manage_performance')
            if request.form.get('can_manage_goals'): permissions.append('manage_goals')
            if request.form.get('can_manage_kpis'): permissions.append('manage_kpis')
            if request.form.get('can_manage_archives'): permissions.append('manage_archives')
            if request.form.get('can_manage_users'): permissions.append('manage_users')

            new_role = Role(
                name=request.form['name'],
                description=request.form.get('description'),
                permissions=json.dumps(permissions, ensure_ascii=False)
            )

            db.session.add(new_role)
            db.session.commit()
            flash('تمت إضافة الدور بنجاح!', 'success')
            return redirect(url_for('manage_roles'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الدور: {e}', 'danger')

    return render_template('add_role.html')

@app.route('/edit_role/<int:role_id>', methods=['GET', 'POST'])
def edit_role(role_id):
    """تعديل الدور"""
    role = Role.query.get_or_404(role_id)

    if request.method == 'POST':
        try:
            import json

            # جمع الصلاحيات المحددة
            permissions = []
            if request.form.get('can_view_employees'): permissions.append('view_employees')
            if request.form.get('can_edit_employees'): permissions.append('edit_employees')
            if request.form.get('can_delete_employees'): permissions.append('delete_employees')
            if request.form.get('can_manage_departments'): permissions.append('manage_departments')
            if request.form.get('can_manage_attendance'): permissions.append('manage_attendance')
            if request.form.get('can_manage_leaves'): permissions.append('manage_leaves')
            if request.form.get('can_view_reports'): permissions.append('view_reports')
            if request.form.get('can_manage_performance'): permissions.append('manage_performance')
            if request.form.get('can_manage_goals'): permissions.append('manage_goals')
            if request.form.get('can_manage_kpis'): permissions.append('manage_kpis')
            if request.form.get('can_manage_archives'): permissions.append('manage_archives')
            if request.form.get('can_manage_users'): permissions.append('manage_users')

            role.name = request.form['name']
            role.description = request.form.get('description')
            role.permissions = json.dumps(permissions, ensure_ascii=False)

            db.session.commit()
            flash('تم تحديث الدور بنجاح!', 'success')
            return redirect(url_for('manage_roles'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الدور: {e}', 'danger')

    # تحليل الصلاحيات الحالية
    current_permissions = []
    if role.permissions:
        try:
            import json
            current_permissions = json.loads(role.permissions)
        except:
            current_permissions = []

    return render_template('edit_role.html', role=role, current_permissions=current_permissions)

@app.route('/delete_role/<int:role_id>', methods=['POST'])
def delete_role(role_id):
    """حذف الدور"""
    role = Role.query.get_or_404(role_id)
    try:
        # التحقق من عدم وجود مستخدمين مرتبطين بهذا الدور
        users_with_role = User.query.filter_by(role_id=role_id).count()
        if users_with_role > 0:
            flash(f'لا يمكن حذف الدور لأنه مرتبط بـ {users_with_role} مستخدم', 'danger')
        else:
            db.session.delete(role)
            db.session.commit()
            flash('تم حذف الدور بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الدور: {e}', 'danger')

    return redirect(url_for('manage_roles'))

@app.route('/manage_users')
def manage_users():
    """عرض وإدارة المستخدمين"""
    users = User.query.all()
    roles = Role.query.all()
    return render_template('manage_users.html', users=users, roles=roles)

@app.route('/activity_logs')
def activity_logs():
    """عرض سجل العمليات"""
    logs = ActivityLog.query.order_by(ActivityLog.timestamp.desc()).limit(100).all()
    return render_template('activity_logs.html', logs=logs)

def log_activity(user_id, action, table_name=None, record_id=None, old_values=None, new_values=None):
    """تسجيل نشاط في سجل العمليات"""
    try:
        import json

        log = ActivityLog(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=json.dumps(old_values, ensure_ascii=False) if old_values else None,
            new_values=json.dumps(new_values, ensure_ascii=False) if new_values else None,
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )

        db.session.add(log)
        db.session.commit()

    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {e}")

# مسارات إضافية للنظام المتقدم
@app.route('/api/dashboard_stats')
def api_dashboard_stats():
    """API لجلب إحصائيات لوحة التحكم في الوقت الفعلي"""
    from datetime import date

    today = date.today()
    stats = {
        'total_employees': Employee.query.count(),
        'present_today': Attendance.query.filter_by(date=today, status='حاضر').count(),
        'pending_leaves': Leave.query.filter_by(status='Pending').count(),
        'avg_performance': round(db.session.query(db.func.avg(PerformanceReview.overall_rating)).scalar() or 0, 1),
        'active_goals': Goal.query.filter_by(status='Active').count(),
        'unread_notifications': Notification.query.filter_by(is_read=False).count()
    }

    return jsonify(stats)

@app.route('/generate_performance_report/<int:employee_id>')
def generate_performance_report(employee_id):
    """إنشاء تقرير أداء شامل للموظف"""
    employee = Employee.query.get_or_404(employee_id)
    reviews = PerformanceReview.query.filter_by(employee_id=employee_id).order_by(PerformanceReview.review_period_start.desc()).all()
    goals = Goal.query.filter_by(employee_id=employee_id).all()
    kpis = KPI.query.filter_by(employee_id=employee_id).all()

    return render_template('performance_report.html',
                          employee=employee,
                          reviews=reviews,
                          goals=goals,
                          kpis=kpis)

@app.route('/bulk_archive', methods=['POST'])
def bulk_archive():
    """أرشفة مجمعة للبيانات"""
    try:
        archive_type = request.form.get('archive_type')
        cutoff_date = datetime.strptime(request.form.get('cutoff_date'), '%Y-%m-%d').date()

        archived_count = 0

        if archive_type == 'attendance':
            old_records = Attendance.query.filter(Attendance.date < cutoff_date).all()
            for record in old_records:
                # إنشاء سجل أرشيف
                import json
                data = {
                    'employee_id': record.employee_id,
                    'date': record.date.isoformat(),
                    'status': record.status
                }

                archive = Archive(
                    table_name='Attendance',
                    original_id=record.id,
                    data=json.dumps(data, ensure_ascii=False),
                    archived_by=1,
                    reason=f'أرشفة مجمعة للبيانات الأقدم من {cutoff_date}'
                )

                db.session.add(archive)
                db.session.delete(record)
                archived_count += 1

        elif archive_type == 'leaves':
            old_records = Leave.query.filter(
                Leave.end_date < cutoff_date,
                Leave.status.in_(['Approved', 'Rejected'])
            ).all()

            for record in old_records:
                import json
                data = {
                    'employee_id': record.employee_id,
                    'leave_type': record.leave_type,
                    'start_date': record.start_date.isoformat(),
                    'end_date': record.end_date.isoformat(),
                    'status': record.status,
                    'request_date': record.request_date.isoformat()
                }

                archive = Archive(
                    table_name='Leave',
                    original_id=record.id,
                    data=json.dumps(data, ensure_ascii=False),
                    archived_by=1,
                    reason=f'أرشفة مجمعة للإجازات المنتهية قبل {cutoff_date}'
                )

                db.session.add(archive)
                db.session.delete(record)
                archived_count += 1

        db.session.commit()
        flash(f'تم أرشفة {archived_count} سجل بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء الأرشفة المجمعة: {e}', 'danger')

    return redirect(url_for('manage_archives'))

# مسارات إدارة المشاريع
@app.route('/manage_projects')
def manage_projects():
    """عرض وإدارة المشاريع"""
    search_query = request.args.get('search', '')
    filter_status = request.args.get('status')
    filter_department_id = request.args.get('department_id', type=int)

    query = Project.query.join(Employee, Project.manager_id == Employee.id)

    if search_query:
        query = query.filter(Project.name.contains(search_query))
    if filter_status:
        query = query.filter(Project.status == filter_status)
    if filter_department_id:
        query = query.filter(Project.department_id == filter_department_id)

    projects = query.order_by(Project.created_date.desc()).all()
    departments = Department.query.all()

    return render_template('manage_projects.html',
                          projects=projects,
                          departments=departments,
                          search_query=search_query,
                          filter_status=filter_status,
                          filter_department_id=filter_department_id)

@app.route('/add_project', methods=['GET', 'POST'])
def add_project():
    """إضافة مشروع جديد"""
    if request.method == 'POST':
        try:
            start_date = datetime.strptime(request.form['start_date'], '%Y-%m-%d').date()
            end_date = None
            if request.form.get('end_date'):
                end_date = datetime.strptime(request.form['end_date'], '%Y-%m-%d').date()

            new_project = Project(
                name=request.form['name'],
                description=request.form.get('description'),
                start_date=start_date,
                end_date=end_date,
                status=request.form.get('status', 'Active'),
                priority=request.form.get('priority', 'Medium'),
                budget=float(request.form['budget']) if request.form.get('budget') else None,
                manager_id=request.form['manager_id'],
                department_id=request.form.get('department_id') if request.form.get('department_id') else None
            )

            db.session.add(new_project)
            db.session.commit()
            flash('تم إضافة المشروع بنجاح!', 'success')
            return redirect(url_for('manage_projects'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المشروع: {e}', 'danger')

    employees = Employee.query.all()
    departments = Department.query.all()
    return render_template('add_project.html', employees=employees, departments=departments)

@app.route('/manage_tasks')
def manage_tasks():
    """عرض وإدارة المهام"""
    search_query = request.args.get('search', '')
    filter_status = request.args.get('status')
    filter_project_id = request.args.get('project_id', type=int)
    filter_assigned_to = request.args.get('assigned_to', type=int)

    query = Task.query.join(Employee, Task.assigned_to == Employee.id)

    if search_query:
        query = query.filter(Task.title.contains(search_query))
    if filter_status:
        query = query.filter(Task.status == filter_status)
    if filter_project_id:
        query = query.filter(Task.project_id == filter_project_id)
    if filter_assigned_to:
        query = query.filter(Task.assigned_to == filter_assigned_to)

    tasks = query.order_by(Task.created_date.desc()).all()
    projects = Project.query.all()
    employees = Employee.query.all()

    return render_template('manage_tasks.html',
                          tasks=tasks,
                          projects=projects,
                          employees=employees,
                          search_query=search_query,
                          filter_status=filter_status,
                          filter_project_id=filter_project_id,
                          filter_assigned_to=filter_assigned_to)

@app.route('/add_task', methods=['GET', 'POST'])
def add_task():
    """إضافة مهمة جديدة"""
    if request.method == 'POST':
        try:
            due_date = None
            if request.form.get('due_date'):
                due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d').date()

            new_task = Task(
                title=request.form['title'],
                description=request.form.get('description'),
                project_id=request.form.get('project_id') if request.form.get('project_id') else None,
                assigned_to=request.form['assigned_to'],
                created_by=1,  # يجب تحديث هذا ليكون المستخدم الحالي
                priority=request.form.get('priority', 'Medium'),
                status=request.form.get('status', 'To Do'),
                due_date=due_date,
                estimated_hours=float(request.form['estimated_hours']) if request.form.get('estimated_hours') else None
            )

            db.session.add(new_task)
            db.session.commit()
            flash('تم إضافة المهمة بنجاح!', 'success')
            return redirect(url_for('manage_tasks'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة المهمة: {e}', 'danger')

    projects = Project.query.all()
    employees = Employee.query.all()
    return render_template('add_task.html', projects=projects, employees=employees)

@app.route('/update_task_progress/<int:task_id>', methods=['POST'])
def update_task_progress(task_id):
    """تحديث تقدم المهمة"""
    try:
        task = Task.query.get_or_404(task_id)
        progress = int(request.form['progress'])
        actual_hours = float(request.form.get('actual_hours', 0))

        task.progress_percentage = progress
        task.actual_hours = actual_hours

        if progress == 100 and task.status != 'Done':
            task.status = 'Done'
            task.completed_date = datetime.utcnow()

        db.session.commit()
        flash('تم تحديث تقدم المهمة بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث المهمة: {e}', 'danger')

    return redirect(url_for('manage_tasks'))

@app.route('/manage_salaries')
def manage_salaries():
    """عرض وإدارة الرواتب"""
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_month = request.args.get('month')

    query = Salary.query.join(Employee)

    if search_query:
        query = query.filter(Employee.full_name.contains(search_query))
    if filter_employee_id:
        query = query.filter(Salary.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Salary.status == filter_status)
    if filter_month:
        month_date = datetime.strptime(filter_month, '%Y-%m').date()
        query = query.filter(
            Salary.pay_period_start >= month_date,
            Salary.pay_period_start < month_date.replace(month=month_date.month + 1 if month_date.month < 12 else 1,
                                                        year=month_date.year + (1 if month_date.month == 12 else 0))
        )

    salaries = query.order_by(Salary.created_date.desc()).all()
    employees = Employee.query.all()

    return render_template('manage_salaries.html',
                          salaries=salaries,
                          employees=employees,
                          search_query=search_query,
                          filter_employee_id=filter_employee_id,
                          filter_status=filter_status,
                          filter_month=filter_month)

@app.route('/add_salary', methods=['GET', 'POST'])
def add_salary():
    """إضافة راتب جديد"""
    if request.method == 'POST':
        try:
            pay_period_start = datetime.strptime(request.form['pay_period_start'], '%Y-%m-%d').date()
            pay_period_end = datetime.strptime(request.form['pay_period_end'], '%Y-%m-%d').date()
            payment_date = None
            if request.form.get('payment_date'):
                payment_date = datetime.strptime(request.form['payment_date'], '%Y-%m-%d').date()

            basic_salary = float(request.form['basic_salary'])
            allowances = float(request.form.get('allowances', 0))
            overtime_hours = float(request.form.get('overtime_hours', 0))
            overtime_rate = float(request.form.get('overtime_rate', 0))
            deductions = float(request.form.get('deductions', 0))
            bonus = float(request.form.get('bonus', 0))

            # حساب الراتب الصافي
            overtime_pay = overtime_hours * overtime_rate
            gross_salary = basic_salary + allowances + overtime_pay + bonus
            net_salary = gross_salary - deductions

            new_salary = Salary(
                employee_id=request.form['employee_id'],
                basic_salary=basic_salary,
                allowances=allowances,
                overtime_hours=overtime_hours,
                overtime_rate=overtime_rate,
                deductions=deductions,
                bonus=bonus,
                net_salary=net_salary,
                pay_period_start=pay_period_start,
                pay_period_end=pay_period_end,
                payment_date=payment_date,
                status=request.form.get('status', 'Pending'),
                notes=request.form.get('notes')
            )

            db.session.add(new_salary)
            db.session.commit()
            flash('تم إضافة الراتب بنجاح!', 'success')
            return redirect(url_for('manage_salaries'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الراتب: {e}', 'danger')

    employees = Employee.query.all()
    return render_template('add_salary.html', employees=employees)

@app.route('/calculate_salary/<int:employee_id>')
def calculate_salary(employee_id):
    """حساب الراتب التلقائي للموظف"""
    employee = Employee.query.get_or_404(employee_id)

    # حساب ساعات العمل الإضافية من سجلات الحضور
    current_month = datetime.now().date().replace(day=1)
    next_month = current_month.replace(month=current_month.month + 1 if current_month.month < 12 else 1,
                                     year=current_month.year + (1 if current_month.month == 12 else 0))

    # حساب أيام الحضور في الشهر الحالي
    attendance_days = Attendance.query.filter(
        Attendance.employee_id == employee_id,
        Attendance.date >= current_month,
        Attendance.date < next_month,
        Attendance.status == 'حاضر'
    ).count()

    # حساب أيام الإجازات المعتمدة
    leave_days = db.session.query(db.func.sum(
        db.func.julianday(Leave.end_date) - db.func.julianday(Leave.start_date) + 1
    )).filter(
        Leave.employee_id == employee_id,
        Leave.start_date >= current_month,
        Leave.end_date < next_month,
        Leave.status == 'Approved'
    ).scalar() or 0

    # إرجاع البيانات المحسوبة
    return jsonify({
        'attendance_days': attendance_days,
        'leave_days': int(leave_days),
        'working_days': attendance_days + int(leave_days),
        'suggested_basic_salary': 5000,  # يمكن تخصيص هذا حسب الدرجة الوظيفية
        'suggested_allowances': 500
    })

@app.route('/project_details/<int:project_id>')
def project_details(project_id):
    """عرض تفاصيل المشروع"""
    project = Project.query.get_or_404(project_id)
    tasks = Task.query.filter_by(project_id=project_id).all()

    # حساب إحصائيات المشروع
    total_tasks = len(tasks)
    completed_tasks = len([t for t in tasks if t.status == 'Done'])
    in_progress_tasks = len([t for t in tasks if t.status == 'In Progress'])

    # حساب التقدم الإجمالي
    if total_tasks > 0:
        overall_progress = sum([t.progress_percentage for t in tasks]) / total_tasks
    else:
        overall_progress = 0

    stats = {
        'total_tasks': total_tasks,
        'completed_tasks': completed_tasks,
        'in_progress_tasks': in_progress_tasks,
        'overall_progress': round(overall_progress, 1)
    }

    return redirect(url_for('manage_tasks'))

@app.route('/project_details/<int:project_id>')
def project_details(project_id):
    """عرض تفاصيل المشروع"""
    project = Project.query.get_or_404(project_id)
    tasks = Task.query.filter_by(project_id=project_id).all()

    # حساب إحصائيات المشروع
    total_tasks = len(tasks)
    completed_tasks = len([t for t in tasks if t.status == 'Done'])
    in_progress_tasks = len([t for t in tasks if t.status == 'In Progress'])

    # حساب التقدم الإجمالي
    if total_tasks > 0:
        overall_progress = sum([t.progress_percentage for t in tasks]) / total_tasks
    else:
        overall_progress = 0

    stats = {
        'total_tasks': total_tasks,
        'completed_tasks': completed_tasks,
        'in_progress_tasks': in_progress_tasks,
        'overall_progress': round(overall_progress, 1)
    }

    return render_template('project_details.html',
                          project=project,
                          tasks=tasks,
                          stats=stats)

# مسارات إضافية للميزات الجديدة
@app.route('/delete_project/<int:project_id>', methods=['POST'])
def delete_project(project_id):
    """حذف مشروع"""
    try:
        project = Project.query.get_or_404(project_id)
        # حذف المهام المرتبطة أولاً
        Task.query.filter_by(project_id=project_id).delete()
        db.session.delete(project)
        db.session.commit()
        flash('تم حذف المشروع بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المشروع: {e}', 'danger')

    return redirect(url_for('manage_projects'))

@app.route('/delete_task/<int:task_id>', methods=['POST'])
def delete_task(task_id):
    """حذف مهمة"""
    try:
        task = Task.query.get_or_404(task_id)
        db.session.delete(task)
        db.session.commit()
        flash('تم حذف المهمة بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المهمة: {e}', 'danger')

    return redirect(url_for('manage_tasks'))

@app.route('/mark_salary_paid/<int:salary_id>', methods=['POST'])
def mark_salary_paid(salary_id):
    """تأكيد دفع الراتب"""
    try:
        salary = Salary.query.get_or_404(salary_id)
        salary.status = 'Paid'
        salary.payment_date = datetime.now().date()
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/delete_training/<int:training_id>', methods=['POST'])
def delete_training(training_id):
    """حذف تدريب"""
    try:
        training = Training.query.get_or_404(training_id)
        # حذف التسجيلات المرتبطة أولاً
        TrainingEnrollment.query.filter_by(training_id=training_id).delete()
        db.session.delete(training)
        db.session.commit()
        flash('تم حذف التدريب بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف التدريب: {e}', 'danger')

    return redirect(url_for('manage_trainings'))

@app.route('/employee_dashboard/<int:employee_id>')
def employee_dashboard(employee_id):
    """لوحة تحكم خاصة بالموظف"""
    employee = Employee.query.get_or_404(employee_id)

    # المهام المكلف بها
    my_tasks = Task.query.filter_by(assigned_to=employee_id).order_by(Task.due_date.asc()).limit(5).all()

    # الأهداف الحالية
    my_goals = Goal.query.filter_by(employee_id=employee_id, status='Active').limit(5).all()

    # التدريبات المسجل بها
    my_trainings = db.session.query(Training).join(TrainingEnrollment).filter(
        TrainingEnrollment.employee_id == employee_id,
        Training.status.in_(['Planned', 'Ongoing'])
    ).limit(5).all()

    # آخر تقييم أداء
    latest_review = PerformanceReview.query.filter_by(employee_id=employee_id).order_by(
        PerformanceReview.created_date.desc()
    ).first()

    # إحصائيات سريعة
    stats = {
        'pending_tasks': Task.query.filter_by(assigned_to=employee_id, status='To Do').count(),
        'active_goals': Goal.query.filter_by(employee_id=employee_id, status='Active').count(),
        'completed_trainings': db.session.query(TrainingEnrollment).join(Training).filter(
            TrainingEnrollment.employee_id == employee_id,
            Training.status == 'Completed'
        ).count(),
        'avg_performance': latest_review.overall_rating if latest_review else 0
    }

    return render_template('employee_dashboard.html',
                          employee=employee,
                          my_tasks=my_tasks,
                          my_goals=my_goals,
                          my_trainings=my_trainings,
                          latest_review=latest_review,
                          stats=stats)

# مسارات إضافية للميزات الجديدة
@app.route('/export_analytics_excel')
def export_analytics_excel():
    """تصدير التحليلات إلى Excel"""
    try:
        import pandas as pd
        from datetime import datetime
        import tempfile

        # جمع البيانات
        employees_data = []
        for emp in Employee.query.all():
            employees_data.append({
                'الاسم': emp.full_name,
                'الرقم الوظيفي': emp.employee_id,
                'المنصب': emp.job_title,
                'القسم': emp.department.name if emp.department else 'غير محدد',
                'تاريخ التعيين': emp.appointment_date,
                'سنوات الخدمة': emp.years_of_service
            })

        # إنشاء ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')

        # إنشاء ملف Excel
        with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
            pd.DataFrame(employees_data).to_excel(writer, sheet_name='الموظفين', index=False)

            # إضافة ورقة للمشاريع
            projects_data = []
            for project in Project.query.all():
                projects_data.append({
                    'اسم المشروع': project.name,
                    'المدير': project.manager.full_name,
                    'الحالة': project.status,
                    'التقدم': f"{project.progress_percentage}%",
                    'الميزانية': project.budget or 0
                })
            pd.DataFrame(projects_data).to_excel(writer, sheet_name='المشاريع', index=False)

        return send_file(temp_file.name, as_attachment=True, download_name='analytics_report.xlsx')

    except Exception as e:
        flash(f'حدث خطأ في التصدير: {e}', 'danger')
        return redirect(url_for('advanced_analytics'))

@app.route('/api/analytics_data')
def api_analytics_data():
    """API لجلب بيانات التحليلات"""
    try:
        data = {
            'total_employees': Employee.query.count(),
            'active_projects': Project.query.filter_by(status='Active').count(),
            'completed_trainings': Training.query.filter_by(status='Completed').count(),
            'pending_tasks': Task.query.filter_by(status='To Do').count(),
            'timestamp': datetime.now().isoformat()
        }
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/bulk_salary_generation', methods=['GET', 'POST'])
def bulk_salary_generation():
    """إنشاء رواتب مجمعة للموظفين"""
    if request.method == 'POST':
        try:
            month = request.form['month']
            department_id = request.form.get('department_id')

            # تحديد الموظفين
            query = Employee.query
            if department_id:
                query = query.filter_by(department_id=department_id)

            employees = query.all()
            created_count = 0

            for employee in employees:
                # التحقق من عدم وجود راتب للشهر المحدد
                existing = Salary.query.filter(
                    Salary.employee_id == employee.id,
                    Salary.pay_period_start.like(f"{month}%")
                ).first()

                if not existing:
                    # إنشاء راتب افتراضي
                    month_start = datetime.strptime(f"{month}-01", '%Y-%m-%d').date()
                    month_end = month_start.replace(day=28)  # تبسيط

                    salary = Salary(
                        employee_id=employee.id,
                        basic_salary=5000,  # راتب افتراضي
                        allowances=500,
                        overtime_hours=0,
                        overtime_rate=50,
                        deductions=0,
                        bonus=0,
                        net_salary=5500,
                        pay_period_start=month_start,
                        pay_period_end=month_end,
                        status='Pending'
                    )

                    db.session.add(salary)
                    created_count += 1

            db.session.commit()
            flash(f'تم إنشاء {created_count} راتب بنجاح!', 'success')
            return redirect(url_for('manage_salaries'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {e}', 'danger')

    departments = Department.query.all()
    return render_template('bulk_salary_generation.html', departments=departments)

# إضافة فلتر Jinja2 لتحليل JSON
@app.template_filter('from_json')
def from_json_filter(value):
    """فلتر لتحليل JSON في القوالب"""
    try:
        import json
        return json.loads(value) if value else []
    except:
        return []

# مسارات إضافية للنظام
@app.route('/auto_notifications')
def auto_notifications():
    """تشغيل فحص الإشعارات التلقائية"""
    try:
        # فحص الإجازات المنتهية
        from datetime import date, timedelta

        # إشعارات انتهاء الإجازات
        end_date_threshold = date.today() + timedelta(days=3)
        expiring_leaves = Leave.query.filter(
            Leave.end_date <= end_date_threshold,
            Leave.end_date >= date.today(),
            Leave.status == 'Approved'
        ).all()

        notifications_created = 0

        for leave in expiring_leaves:
            # التحقق من عدم وجود إشعار مسبق
            existing = Notification.query.filter_by(
                notification_type='leave_expiry',
                related_id=leave.id
            ).first()

            if not existing:
                days_left = (leave.end_date - date.today()).days
                notification = Notification(
                    user_id=1,
                    title='انتهاء إجازة قريباً',
                    message=f'ستنتهي إجازة {leave.employee.full_name} ({leave.leave_type}) خلال {days_left} أيام',
                    notification_type='leave_expiry',
                    related_id=leave.id
                )
                db.session.add(notification)
                notifications_created += 1

        # إشعارات تذكير التقييم
        current_year = date.today().year
        employees_without_review = db.session.query(Employee).filter(
            ~Employee.id.in_(
                db.session.query(PerformanceReview.employee_id).filter(
                    db.extract('year', PerformanceReview.review_period_start) == current_year
                )
            )
        ).all()

        for employee in employees_without_review:
            existing = Notification.query.filter_by(
                notification_type='review_reminder',
                related_id=employee.id
            ).first()

            if not existing:
                notification = Notification(
                    user_id=1,
                    title='تذكير تقييم أداء',
                    message=f'يحتاج الموظف {employee.full_name} إلى تقييم أداء لهذا العام',
                    notification_type='review_reminder',
                    related_id=employee.id
                )
                db.session.add(notification)
                notifications_created += 1

        db.session.commit()
        return jsonify({'status': 'success', 'notifications_created': notifications_created})

    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/dashboard_data')
def dashboard_data():
    """جلب بيانات لوحة التحكم للتحديث التلقائي"""
    from datetime import date

    today = date.today()

    # حساب الإحصائيات
    total_employees = Employee.query.count()
    present_today = Attendance.query.filter_by(date=today, status='حاضر').count()
    absent_today = Attendance.query.filter_by(date=today, status='غائب').count()
    pending_leaves = Leave.query.filter_by(status='Pending').count()

    # متوسط التقييمات
    avg_rating = db.session.query(db.func.avg(PerformanceReview.overall_rating)).scalar() or 0

    # الأهداف
    active_goals = Goal.query.filter_by(status='Active').count()
    completed_goals = Goal.query.filter_by(status='Completed').count()

    # المؤشرات
    total_kpis = KPI.query.count()

    # الإشعارات
    unread_notifications = Notification.query.filter_by(is_read=False).count()

    return jsonify({
        'total_employees': total_employees,
        'present_today': present_today,
        'absent_today': absent_today,
        'attendance_rate': round((present_today / total_employees * 100) if total_employees > 0 else 0, 1),
        'pending_leaves': pending_leaves,
        'avg_rating': round(avg_rating, 1),
        'active_goals': active_goals,
        'completed_goals': completed_goals,
        'goal_completion_rate': round((completed_goals / (active_goals + completed_goals) * 100) if (active_goals + completed_goals) > 0 else 0, 1),
        'total_kpis': total_kpis,
        'unread_notifications': unread_notifications
    })

# مسارات إدارة التدريب
@app.route('/manage_trainings')
def manage_trainings():
    """عرض وإدارة التدريبات"""
    search_query = request.args.get('search', '')
    filter_status = request.args.get('status')
    filter_type = request.args.get('training_type')

    query = Training.query

    if search_query:
        query = query.filter(Training.title.contains(search_query))
    if filter_status:
        query = query.filter(Training.status == filter_status)
    if filter_type:
        query = query.filter(Training.training_type == filter_type)

    trainings = query.order_by(Training.start_date.desc()).all()

    return render_template('manage_trainings.html',
                          trainings=trainings,
                          search_query=search_query,
                          filter_status=filter_status,
                          filter_type=filter_type)

@app.route('/add_training', methods=['GET', 'POST'])
def add_training():
    """إضافة تدريب جديد"""
    if request.method == 'POST':
        try:
            start_date = datetime.strptime(request.form['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(request.form['end_date'], '%Y-%m-%d').date()

            new_training = Training(
                title=request.form['title'],
                description=request.form.get('description'),
                trainer_name=request.form.get('trainer_name'),
                training_type=request.form.get('training_type', 'Internal'),
                start_date=start_date,
                end_date=end_date,
                duration_hours=int(request.form['duration_hours']) if request.form.get('duration_hours') else None,
                max_participants=int(request.form['max_participants']) if request.form.get('max_participants') else None,
                cost=float(request.form['cost']) if request.form.get('cost') else None,
                location=request.form.get('location'),
                status=request.form.get('status', 'Planned')
            )

            db.session.add(new_training)
            db.session.commit()
            flash('تم إضافة التدريب بنجاح!', 'success')
            return redirect(url_for('manage_trainings'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة التدريب: {e}', 'danger')

    return render_template('add_training.html')

@app.route('/training_enrollments/<int:training_id>')
def training_enrollments(training_id):
    """عرض المسجلين في التدريب"""
    training = Training.query.get_or_404(training_id)
    enrollments = TrainingEnrollment.query.filter_by(training_id=training_id).all()
    available_employees = Employee.query.filter(
        ~Employee.id.in_([e.employee_id for e in enrollments])
    ).all()

    return render_template('training_enrollments.html',
                          training=training,
                          enrollments=enrollments,
                          available_employees=available_employees)

@app.route('/enroll_employee', methods=['POST'])
def enroll_employee():
    """تسجيل موظف في تدريب"""
    try:
        training_id = request.form['training_id']
        employee_id = request.form['employee_id']

        # التحقق من عدم وجود تسجيل مسبق
        existing = TrainingEnrollment.query.filter_by(
            training_id=training_id,
            employee_id=employee_id
        ).first()

        if existing:
            flash('الموظف مسجل بالفعل في هذا التدريب!', 'warning')
        else:
            enrollment = TrainingEnrollment(
                training_id=training_id,
                employee_id=employee_id
            )
            db.session.add(enrollment)
            db.session.commit()
            flash('تم تسجيل الموظف في التدريب بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء التسجيل: {e}', 'danger')

    return redirect(url_for('training_enrollments', training_id=training_id))

@app.route('/salary_report')
def salary_report():
    """تقرير الرواتب"""
    filter_month = request.args.get('month')
    filter_department_id = request.args.get('department_id', type=int)

    query = Salary.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)

    if filter_month:
        month_date = datetime.strptime(filter_month, '%Y-%m').date()
        query = query.filter(
            Salary.pay_period_start >= month_date,
            Salary.pay_period_start < month_date.replace(month=month_date.month + 1 if month_date.month < 12 else 1,
                                                        year=month_date.year + (1 if month_date.month == 12 else 0))
        )

    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)

    salaries = query.order_by(Salary.pay_period_start.desc()).all()
    departments = Department.query.all()

    # حساب الإحصائيات
    total_salaries = sum([s.net_salary for s in salaries])
    avg_salary = total_salaries / len(salaries) if salaries else 0

    stats = {
        'total_salaries': total_salaries,
        'avg_salary': avg_salary,
        'total_records': len(salaries),
        'paid_count': len([s for s in salaries if s.status == 'Paid']),
        'pending_count': len([s for s in salaries if s.status == 'Pending'])
    }

    return render_template('salary_report.html',
                          salaries=salaries,
                          departments=departments,
                          stats=stats,
                          filter_month=filter_month,
                          filter_department_id=filter_department_id)

@app.route('/advanced_analytics')
def advanced_analytics():
    """تحليلات متقدمة للنظام"""
    # إحصائيات الأداء
    performance_stats = db.session.query(
        db.func.avg(PerformanceReview.overall_rating).label('avg_rating'),
        db.func.count(PerformanceReview.id).label('total_reviews')
    ).first()

    # إحصائيات الحضور
    today = datetime.now().date()
    month_start = today.replace(day=1)
    attendance_stats = db.session.query(
        Attendance.status,
        db.func.count(Attendance.id).label('count')
    ).filter(Attendance.date >= month_start).group_by(Attendance.status).all()

    # إحصائيات المشاريع
    project_stats = db.session.query(
        Project.status,
        db.func.count(Project.id).label('count')
    ).group_by(Project.status).all()

    # إحصائيات التدريب
    training_stats = db.session.query(
        Training.status,
        db.func.count(Training.id).label('count')
    ).group_by(Training.status).all()

    return render_template('advanced_analytics.html',
                          performance_stats=performance_stats,
                          attendance_stats=attendance_stats,
                          project_stats=project_stats,
                          training_stats=training_stats)

if __name__ == '__main__':
    with app.app_context():
        db.create_all() # إنشاء الجداول في قاعدة البيانات
    app.run(debug=True)