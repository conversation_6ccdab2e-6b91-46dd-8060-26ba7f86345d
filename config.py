# ملف التكوين لنظام إدارة شؤون الموظفين المتقدم

import os
from datetime import timedelta

class Config:
    """إعدادات النظام الأساسية"""
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///employees.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الأمان
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False  # تغيير إلى True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات رفع الملفات
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 ميجابايت
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    
    # إعدادات النسخ الاحتياطية
    BACKUP_FOLDER = 'backups'
    AUTO_BACKUP_ENABLED = True
    AUTO_BACKUP_INTERVAL_HOURS = 24
    BACKUP_RETENTION_DAYS = 30
    
    # إعدادات الإشعارات
    NOTIFICATION_CHECK_INTERVAL_MINUTES = 60
    LEAVE_EXPIRY_NOTIFICATION_DAYS = 7
    REVIEW_REMINDER_ENABLED = True
    GOAL_DEADLINE_NOTIFICATION_DAYS = 7
    
    # إعدادات التقييم
    PERFORMANCE_REVIEW_SCALE_MIN = 1
    PERFORMANCE_REVIEW_SCALE_MAX = 5
    ANNUAL_REVIEW_REQUIRED = True
    
    # إعدادات الأرشفة
    AUTO_ARCHIVE_ENABLED = True
    ARCHIVE_ATTENDANCE_AFTER_DAYS = 365
    ARCHIVE_LEAVES_AFTER_DAYS = 365
    
    # إعدادات التقارير
    REPORTS_CACHE_TIMEOUT = 300  # 5 دقائق
    EXPORT_FORMATS = ['excel', 'pdf', 'csv']
    
    # إعدادات الأمان المتقدمة
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_NUMBERS = True
    PASSWORD_REQUIRE_SPECIAL_CHARS = False
    
    # إعدادات سجل العمليات
    ACTIVITY_LOG_ENABLED = True
    ACTIVITY_LOG_RETENTION_DAYS = 90
    LOG_SENSITIVE_OPERATIONS = True
    
    # إعدادات الأداء
    PAGINATION_PER_PAGE = 20
    SEARCH_RESULTS_LIMIT = 100
    CACHE_TIMEOUT = 300
    
    # إعدادات واجهة المستخدم
    THEME_COLOR_PRIMARY = '#667eea'
    THEME_COLOR_SECONDARY = '#764ba2'
    ENABLE_DARK_MODE = False
    ENABLE_RTL = True
    
    # إعدادات التكامل
    EMAIL_NOTIFICATIONS_ENABLED = False
    SMS_NOTIFICATIONS_ENABLED = False
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق بالإعدادات"""
        
        # إنشاء المجلدات المطلوبة
        folders = [
            Config.UPLOAD_FOLDER,
            Config.BACKUP_FOLDER,
            'logs',
            'exports'
        ]
        
        for folder in folders:
            if not os.path.exists(folder):
                os.makedirs(folder)

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    SQLALCHEMY_ECHO = True  # عرض استعلامات SQL
    
class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    SQLALCHEMY_ECHO = False
    SESSION_COOKIE_SECURE = True
    
    # إعدادات أمان إضافية للإنتاج
    FORCE_HTTPS = True
    HSTS_MAX_AGE = 31536000  # سنة واحدة
    
class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# تحديد التكوين حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# إعدادات الأدوار والصلاحيات الافتراضية
DEFAULT_ROLES = {
    'مدير النظام': {
        'description': 'صلاحيات كاملة لإدارة النظام',
        'permissions': [
            'view_employees', 'edit_employees', 'delete_employees',
            'manage_departments', 'manage_attendance', 'manage_leaves',
            'view_reports', 'manage_performance', 'manage_goals',
            'manage_kpis', 'manage_archives', 'manage_users'
        ]
    },
    'مدير الموارد البشرية': {
        'description': 'إدارة شؤون الموظفين والتقييمات',
        'permissions': [
            'view_employees', 'edit_employees', 'manage_departments',
            'manage_attendance', 'manage_leaves', 'view_reports',
            'manage_performance', 'manage_goals', 'manage_kpis'
        ]
    },
    'مشرف القسم': {
        'description': 'إدارة موظفي القسم والتقييمات',
        'permissions': [
            'view_employees', 'manage_attendance', 'manage_leaves',
            'view_reports', 'manage_performance', 'manage_goals'
        ]
    },
    'موظف': {
        'description': 'صلاحيات أساسية للموظف',
        'permissions': [
            'view_employees'
        ]
    }
}

# إعدادات التقييم الافتراضية
DEFAULT_PERFORMANCE_CRITERIA = [
    'المهارات التقنية',
    'مهارات التواصل',
    'العمل الجماعي',
    'القيادة',
    'الالتزام بالمواعيد',
    'المبادرة'
]

# أنواع الإشعارات
NOTIFICATION_TYPES = {
    'leave_expiry': 'انتهاء إجازة',
    'review_reminder': 'تذكير تقييم',
    'goal_deadline': 'موعد نهائي للهدف',
    'important_date': 'تاريخ مهم',
    'system_alert': 'تنبيه النظام'
}

# أنواع الأهداف
GOAL_PRIORITIES = ['عالية', 'متوسطة', 'منخفضة']
GOAL_STATUSES = ['نشط', 'مكتمل', 'ملغي', 'مؤجل']

# فترات قياس المؤشرات
KPI_MEASUREMENT_PERIODS = ['يومي', 'أسبوعي', 'شهري', 'ربع سنوي', 'سنوي']
