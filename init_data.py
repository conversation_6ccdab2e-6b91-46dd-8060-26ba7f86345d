#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تهيئة البيانات الأولية لنظام إدارة شؤون الموظفين المتقدم
يقوم بإنشاء البيانات الأساسية والأدوار والمستخدمين الافتراضيين
"""

from app import app, db, User, Role, Employee, Department, PerformanceReview, Goal, KPI, Notification
from datetime import datetime, date, timedelta
import json

def create_default_roles():
    """إنشاء الأدوار الافتراضية"""
    
    roles_data = [
        {
            'name': 'مدير النظام',
            'description': 'صلاحيات كاملة لإدارة النظام',
            'permissions': [
                'view_employees', 'edit_employees', 'delete_employees',
                'manage_departments', 'manage_attendance', 'manage_leaves',
                'view_reports', 'manage_performance', 'manage_goals',
                'manage_kpis', 'manage_archives', 'manage_users'
            ]
        },
        {
            'name': 'مدير الموارد البشرية',
            'description': 'إدارة شؤون الموظفين والتقييمات',
            'permissions': [
                'view_employees', 'edit_employees', 'manage_departments',
                'manage_attendance', 'manage_leaves', 'view_reports',
                'manage_performance', 'manage_goals', 'manage_kpis'
            ]
        },
        {
            'name': 'مشرف القسم',
            'description': 'إدارة موظفي القسم والتقييمات',
            'permissions': [
                'view_employees', 'manage_attendance', 'manage_leaves',
                'view_reports', 'manage_performance', 'manage_goals'
            ]
        },
        {
            'name': 'موظف',
            'description': 'صلاحيات أساسية للموظف',
            'permissions': ['view_employees']
        }
    ]
    
    for role_data in roles_data:
        existing_role = Role.query.filter_by(name=role_data['name']).first()
        if not existing_role:
            role = Role(
                name=role_data['name'],
                description=role_data['description'],
                permissions=json.dumps(role_data['permissions'], ensure_ascii=False)
            )
            db.session.add(role)
            print(f"تم إنشاء الدور: {role_data['name']}")

def create_default_users():
    """إنشاء المستخدمين الافتراضيين"""
    
    # الحصول على دور مدير النظام
    admin_role = Role.query.filter_by(name='مدير النظام').first()
    hr_role = Role.query.filter_by(name='مدير الموارد البشرية').first()
    
    users_data = [
        {
            'username': 'admin',
            'password': 'admin123',
            'email': '<EMAIL>',
            'full_name': 'مدير النظام',
            'role_id': admin_role.id if admin_role else None
        },
        {
            'username': 'hr_manager',
            'password': 'hr123',
            'email': '<EMAIL>',
            'full_name': 'مدير الموارد البشرية',
            'role_id': hr_role.id if hr_role else None
        }
    ]
    
    for user_data in users_data:
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if not existing_user:
            user = User(
                username=user_data['username'],
                password=user_data['password'],  # في الإنتاج يجب تشفير كلمة المرور
                email=user_data['email'],
                full_name=user_data['full_name'],
                role_id=user_data['role_id']
            )
            db.session.add(user)
            print(f"تم إنشاء المستخدم: {user_data['username']}")

def create_sample_departments():
    """إنشاء أقسام تجريبية"""
    
    departments_data = [
        {'name': 'الموارد البشرية', 'description': 'إدارة شؤون الموظفين والتوظيف'},
        {'name': 'تقنية المعلومات', 'description': 'تطوير وصيانة الأنظمة التقنية'},
        {'name': 'المالية والمحاسبة', 'description': 'إدارة الشؤون المالية والمحاسبية'},
        {'name': 'التسويق والمبيعات', 'description': 'تسويق المنتجات وإدارة المبيعات'},
        {'name': 'العمليات', 'description': 'إدارة العمليات التشغيلية'}
    ]
    
    for dept_data in departments_data:
        existing_dept = Department.query.filter_by(name=dept_data['name']).first()
        if not existing_dept:
            department = Department(
                name=dept_data['name'],
                description=dept_data['description']
            )
            db.session.add(department)
            print(f"تم إنشاء القسم: {dept_data['name']}")

def create_sample_employees():
    """إنشاء موظفين تجريبيين"""
    
    # الحصول على الأقسام
    hr_dept = Department.query.filter_by(name='الموارد البشرية').first()
    it_dept = Department.query.filter_by(name='تقنية المعلومات').first()
    finance_dept = Department.query.filter_by(name='المالية والمحاسبة').first()
    
    employees_data = [
        {
            'employee_id': 'EMP001',
            'full_name': 'أحمد محمد علي',
            'email': '<EMAIL>',
            'phone': '0501234567',
            'position': 'مطور برمجيات',
            'department_id': it_dept.id if it_dept else None,
            'hire_date': date(2022, 1, 15),
            'salary': 8000.0
        },
        {
            'employee_id': 'EMP002',
            'full_name': 'فاطمة أحمد حسن',
            'email': '<EMAIL>',
            'phone': '0507654321',
            'position': 'أخصائية موارد بشرية',
            'department_id': hr_dept.id if hr_dept else None,
            'hire_date': date(2021, 6, 10),
            'salary': 7000.0
        },
        {
            'employee_id': 'EMP003',
            'full_name': 'محمد سالم الأحمد',
            'email': '<EMAIL>',
            'phone': '0509876543',
            'position': 'محاسب أول',
            'department_id': finance_dept.id if finance_dept else None,
            'hire_date': date(2020, 3, 20),
            'salary': 7500.0
        }
    ]
    
    for emp_data in employees_data:
        existing_emp = Employee.query.filter_by(employee_id=emp_data['employee_id']).first()
        if not existing_emp:
            employee = Employee(
                employee_id=emp_data['employee_id'],
                full_name=emp_data['full_name'],
                email=emp_data['email'],
                phone=emp_data['phone'],
                position=emp_data['position'],
                department_id=emp_data['department_id'],
                hire_date=emp_data['hire_date'],
                salary=emp_data['salary']
            )
            db.session.add(employee)
            print(f"تم إنشاء الموظف: {emp_data['full_name']}")

def create_sample_goals():
    """إنشاء أهداف تجريبية"""
    
    employees = Employee.query.all()
    if not employees:
        return
    
    goals_data = [
        {
            'title': 'تطوير مهارات البرمجة',
            'description': 'تعلم تقنيات برمجة جديدة وتطبيقها في المشاريع',
            'target_date': date.today() + timedelta(days=90),
            'priority': 'High',
            'progress_percentage': 25
        },
        {
            'title': 'تحسين كفاءة العمل',
            'description': 'زيادة الإنتاجية بنسبة 20% خلال الربع القادم',
            'target_date': date.today() + timedelta(days=120),
            'priority': 'Medium',
            'progress_percentage': 40
        },
        {
            'title': 'تطوير مهارات القيادة',
            'description': 'حضور دورات تدريبية في القيادة وإدارة الفرق',
            'target_date': date.today() + timedelta(days=180),
            'priority': 'High',
            'progress_percentage': 10
        }
    ]
    
    for i, goal_data in enumerate(goals_data):
        if i < len(employees):
            goal = Goal(
                employee_id=employees[i].id,
                title=goal_data['title'],
                description=goal_data['description'],
                target_date=goal_data['target_date'],
                priority=goal_data['priority'],
                progress_percentage=goal_data['progress_percentage']
            )
            db.session.add(goal)
            print(f"تم إنشاء الهدف: {goal_data['title']}")

def create_sample_kpis():
    """إنشاء مؤشرات أداء تجريبية"""
    
    employees = Employee.query.all()
    if not employees:
        return
    
    kpis_data = [
        {
            'kpi_name': 'عدد المشاريع المكتملة',
            'target_value': 10,
            'current_value': 7,
            'unit': 'مشروع',
            'measurement_period': 'شهري'
        },
        {
            'kpi_name': 'نسبة رضا العملاء',
            'target_value': 95,
            'current_value': 88,
            'unit': '%',
            'measurement_period': 'ربع سنوي'
        },
        {
            'kpi_name': 'ساعات التدريب',
            'target_value': 40,
            'current_value': 25,
            'unit': 'ساعة',
            'measurement_period': 'سنوي'
        }
    ]
    
    for i, kpi_data in enumerate(kpis_data):
        if i < len(employees):
            kpi = KPI(
                employee_id=employees[i].id,
                kpi_name=kpi_data['kpi_name'],
                target_value=kpi_data['target_value'],
                current_value=kpi_data['current_value'],
                unit=kpi_data['unit'],
                measurement_period=kpi_data['measurement_period']
            )
            db.session.add(kpi)
            print(f"تم إنشاء المؤشر: {kpi_data['kpi_name']}")

def create_sample_notifications():
    """إنشاء إشعارات تجريبية"""
    
    notifications_data = [
        {
            'user_id': 1,
            'title': 'مرحباً بك في النظام المتقدم',
            'message': 'تم تفعيل الميزات الجديدة بنجاح. استكشف الإمكانيات المتطورة!',
            'notification_type': 'system_alert'
        },
        {
            'user_id': 1,
            'title': 'تذكير تقييم أداء',
            'message': 'يوجد 3 موظفين يحتاجون إلى تقييم أداء لهذا العام',
            'notification_type': 'review_reminder'
        },
        {
            'user_id': 1,
            'title': 'نسخة احتياطية تلقائية',
            'message': 'تم إنشاء نسخة احتياطية تلقائية بنجاح',
            'notification_type': 'system_alert'
        }
    ]
    
    for notif_data in notifications_data:
        notification = Notification(
            user_id=notif_data['user_id'],
            title=notif_data['title'],
            message=notif_data['message'],
            notification_type=notif_data['notification_type']
        )
        db.session.add(notification)
        print(f"تم إنشاء الإشعار: {notif_data['title']}")

def init_database():
    """تهيئة قاعدة البيانات بالبيانات الأولية"""
    
    print("بدء تهيئة قاعدة البيانات...")
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        print("تم إنشاء جداول قاعدة البيانات")
        
        # إنشاء الأدوار الافتراضية
        create_default_roles()
        
        # إنشاء المستخدمين الافتراضيين
        create_default_users()
        
        # إنشاء الأقسام التجريبية
        create_sample_departments()
        
        # حفظ التغييرات
        db.session.commit()
        print("تم حفظ الأدوار والمستخدمين والأقسام")
        
        # إنشاء الموظفين التجريبيين
        create_sample_employees()
        
        # حفظ الموظفين
        db.session.commit()
        print("تم حفظ الموظفين التجريبيين")
        
        # إنشاء الأهداف التجريبية
        create_sample_goals()
        
        # إنشاء مؤشرات الأداء التجريبية
        create_sample_kpis()
        
        # إنشاء الإشعارات التجريبية
        create_sample_notifications()
        
        # حفظ جميع التغييرات
        db.session.commit()
        print("تم إنشاء البيانات التجريبية بنجاح")
        
        print("\n" + "="*50)
        print("تمت تهيئة النظام بنجاح!")
        print("="*50)
        print("معلومات تسجيل الدخول:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("="*50)
        print("أو:")
        print("اسم المستخدم: hr_manager")
        print("كلمة المرور: hr123")
        print("="*50)

if __name__ == '__main__':
    init_database()
