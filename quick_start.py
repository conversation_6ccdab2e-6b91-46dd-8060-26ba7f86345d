#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل سريع لنظام إدارة شؤون الموظفين
للتشغيل السريع بدون إعدادات معقدة
"""

import os
import sys
from datetime import datetime

def quick_setup():
    """إعداد سريع للنظام"""
    print("⚡ التشغيل السريع لنظام إدارة شؤون الموظفين")
    print("=" * 50)
    
    # التحقق من وجود ملف التطبيق
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود!")
        sys.exit(1)
    
    # إنشاء المجلدات الأساسية
    folders = ['uploads', 'instance']
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"📁 تم إنشاء مجلد: {folder}")
    
    print("✅ الإعداد السريع مكتمل!")
    print("\n🔐 بيانات الدخول الافتراضية:")
    print("   المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n🌐 سيتم فتح النظام على: http://localhost:5000")
    print("=" * 50)

def run_app():
    """تشغيل التطبيق"""
    try:
        from app import app, db
        
        # إنشاء الجداول
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات")
        
        print("🚀 بدء تشغيل الخادم...")
        app.run(debug=True, port=5000)
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المتطلبات: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

if __name__ == '__main__':
    quick_setup()
    run_app()
