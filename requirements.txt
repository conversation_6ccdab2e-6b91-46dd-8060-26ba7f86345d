# متطلبات نظام إدارة شؤون الموظفين المتقدم

# Flask والمكونات الأساسية
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Werkzeug==2.3.7

# قاعدة البيانات
SQLAlchemy==2.0.21

# معالجة البيانات والتقارير
pandas==2.1.1
openpyxl==3.1.2
xlsxwriter==3.1.9

# معالجة التواريخ والأوقات
python-dateutil==2.8.2

# الأمان وتشفير كلمات المرور
bcrypt==4.0.1
cryptography==41.0.4

# معالجة الملفات
Pillow==10.0.1

# إنشاء التقارير PDF
reportlab==4.0.4
weasyprint==59.0

# التحقق من صحة البيانات
WTForms==3.0.1
Flask-WTF==1.1.1

# إدارة المهام المجدولة
APScheduler==3.10.4

# معالجة JSON والبيانات
jsonschema==4.19.1

# إدارة التكوين
python-dotenv==1.0.0

# أدوات التطوير (اختيارية)
Flask-DebugToolbar==0.13.1

# أدوات النسخ الاحتياطي
schedule==1.2.0

# معالجة الصور والملفات
python-magic==0.4.27

# إدارة الذاكرة المؤقتة
Flask-Caching==2.1.0

# أدوات الويب
requests==2.31.0

# معالجة CSV
csv-reader==1.2.0

# أدوات الإحصائيات
numpy==1.25.2
matplotlib==3.7.2
seaborn==0.12.2

# أدوات التصدير المتقدمة
jinja2==3.1.2
MarkupSafe==2.1.3

# أدوات الأمان المتقدمة
itsdangerous==2.1.2

# معالجة الوقت المتقدمة
pytz==2023.3

# أدوات التحليل
scipy==1.11.3

# إدارة الملفات المضغوطة
zipfile36==0.1.3

# أدوات التحقق
email-validator==2.0.0

# معالجة النصوص
arabic-reshaper==3.0.0
python-bidi==0.4.2

# أدوات الشبكة
urllib3==2.0.5

# إدارة الجلسات المتقدمة
Flask-Session==0.5.0

# أدوات المراقبة
psutil==5.9.5

# معالجة الصور المتقدمة
opencv-python==********

# أدوات التشفير المتقدمة
passlib==1.7.4

# إدارة البريد الإلكتروني
Flask-Mail==0.9.1

# أدوات API
Flask-RESTful==0.3.10

# معالجة XML
lxml==4.9.3

# أدوات التحليل المتقدمة
plotly==5.17.0

# إدارة المهام الخلفية
celery==5.3.2
redis==5.0.0

# أدوات الاختبار
pytest==7.4.2
pytest-flask==1.2.0

# أدوات التوثيق
Sphinx==7.2.6

# أدوات الجودة
flake8==6.1.0
black==23.9.1

# أدوات الأداء
gunicorn==21.2.0

# أدوات المراقبة المتقدمة
flask-monitoring-dashboard==3.1.0
