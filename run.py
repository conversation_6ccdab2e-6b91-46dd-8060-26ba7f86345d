#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام إدارة شؤون الموظفين المتقدم
يتضمن إعدادات محسنة للأداء والأمان
"""

import os
import sys
from app import app, db
from config import config

def create_app(config_name=None):
    """إنشاء وتكوين التطبيق"""
    
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # تطبيق التكوين
    app.config.from_object(config.get(config_name, config['default']))
    
    # تهيئة التطبيق
    config[config_name].init_app(app)
    
    return app

def setup_logging():
    """إعداد نظام السجلات"""
    import logging
    from logging.handlers import RotatingFileHandler
    
    if not app.debug:
        # إنشاء مجلد السجلات
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # إعداد ملف السجل
        file_handler = RotatingFileHandler(
            'logs/employees_system.log',
            maxBytes=10240000,  # 10 ميجابايت
            backupCount=10
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('تم بدء تشغيل نظام إدارة شؤون الموظفين')

def setup_database():
    """إعداد قاعدة البيانات"""
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
            
            # التحقق من وجود مستخدم افتراضي
            from app import User, Role
            admin_user = User.query.filter_by(username='admin').first()
            
            if not admin_user:
                print("⚠️  لم يتم العثور على مستخدم افتراضي")
                print("💡 قم بتشغيل: python init_data.py لإنشاء البيانات الأولية")
            else:
                print("✅ تم العثور على المستخدم الافتراضي")
                
        except Exception as e:
            print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
            sys.exit(1)

def setup_scheduler():
    """إعداد المهام المجدولة"""
    try:
        from apscheduler.schedulers.background import BackgroundScheduler
        from app import check_leave_expiry_notifications, check_review_reminders, check_goal_deadlines
        
        scheduler = BackgroundScheduler()
        
        # فحص الإشعارات كل ساعة
        scheduler.add_job(
            func=check_leave_expiry_notifications,
            trigger="interval",
            hours=1,
            id='check_leave_notifications'
        )
        
        scheduler.add_job(
            func=check_review_reminders,
            trigger="interval",
            hours=24,
            id='check_review_reminders'
        )
        
        scheduler.add_job(
            func=check_goal_deadlines,
            trigger="interval",
            hours=12,
            id='check_goal_deadlines'
        )
        
        scheduler.start()
        print("✅ تم تفعيل المهام المجدولة")
        
    except ImportError:
        print("⚠️  APScheduler غير مثبت - المهام المجدولة معطلة")
    except Exception as e:
        print(f"❌ خطأ في إعداد المهام المجدولة: {e}")

def print_startup_info():
    """عرض معلومات بدء التشغيل"""
    print("\n" + "="*60)
    print("🚀 نظام إدارة شؤون الموظفين المتقدم")
    print("="*60)
    print(f"📊 البيئة: {app.config.get('ENV', 'development')}")
    print(f"🔧 وضع التطوير: {'مفعل' if app.debug else 'معطل'}")
    print(f"🗄️  قاعدة البيانات: {app.config['SQLALCHEMY_DATABASE_URI']}")
    print(f"📁 مجلد الرفع: {app.config.get('UPLOAD_FOLDER', 'uploads')}")
    print(f"💾 مجلد النسخ الاحتياطية: {app.config.get('BACKUP_FOLDER', 'backups')}")
    print("="*60)
    print("🌐 الروابط المهمة:")
    print("   الرئيسية: http://localhost:5000")
    print("   لوحة التحكم: http://localhost:5000/dashboard")
    print("   لوحة التحكم المتقدمة: http://localhost:5000/advanced_dashboard")
    print("   التقييمات: http://localhost:5000/manage_performance_reviews")
    print("   الأهداف: http://localhost:5000/manage_goals")
    print("   الإشعارات: http://localhost:5000/notifications")
    print("="*60)
    print("📋 الميزات الجديدة:")
    print("   ⭐ نظام التقييم الوظيفي")
    print("   🎯 إدارة الأهداف والمؤشرات")
    print("   🔔 نظام الإشعارات الذكي")
    print("   🗄️  الأرشفة المتقدمة")
    print("   📈 لوحة التحكم التفاعلية")
    print("   🔐 نظام الصلاحيات المتدرج")
    print("="*60)

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    
    # إنشاء التطبيق
    create_app()
    
    # إعداد السجلات
    setup_logging()
    
    # إعداد قاعدة البيانات
    setup_database()
    
    # إعداد المهام المجدولة
    setup_scheduler()
    
    # عرض معلومات بدء التشغيل
    print_startup_info()
    
    # تشغيل التطبيق
    try:
        app.run(
            host='0.0.0.0',
            port=int(os.environ.get('PORT', 5000)),
            debug=app.config.get('DEBUG', False),
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
