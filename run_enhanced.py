#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل محسن لنظام إدارة شؤون الموظفين المتقدم
يتضمن إعداد البيانات الأولية والتحقق من المتطلبات
"""

import os
import sys
from datetime import datetime, date, timedelta
import json

def check_requirements():
    """التحقق من وجود المتطلبات الأساسية"""
    required_packages = [
        'flask', 'flask_sqlalchemy', 'flask_login', 
        'pandas', 'openpyxl', 'werkzeug'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ المتطلبات التالية غير مثبتة:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 لتثبيت المتطلبات، قم بتشغيل:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات مثبتة بنجاح")
    return True

def setup_database():
    """إعداد قاعدة البيانات والبيانات الأولية"""
    from app import app, db, User, Role, Employee, Department, Project, Training
    
    with app.app_context():
        print("🔧 إنشاء جداول قاعدة البيانات...")
        db.create_all()
        
        # التحقق من وجود المستخدم الإداري
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("👤 إنشاء المستخدم الإداري...")
            
            # إنشاء الأدوار الأساسية
            admin_role = Role(
                name='مدير النظام',
                description='صلاحيات كاملة لإدارة النظام',
                permissions=json.dumps([
                    'manage_employees', 'manage_departments', 'manage_attendance',
                    'manage_leaves', 'manage_documents', 'manage_performance',
                    'manage_goals', 'manage_kpis', 'manage_projects', 'manage_tasks',
                    'manage_salaries', 'manage_trainings', 'manage_archives',
                    'manage_users', 'manage_roles', 'view_reports'
                ], ensure_ascii=False)
            )
            db.session.add(admin_role)
            db.session.commit()
            
            # إنشاء المستخدم الإداري
            admin_user = User(
                username='admin',
                password='admin123',
                email='<EMAIL>',
                full_name='مدير النظام',
                role_id=admin_role.id
            )
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الإداري (admin/admin123)")
        
        # إنشاء أقسام تجريبية
        if Department.query.count() == 0:
            print("🏢 إنشاء الأقسام التجريبية...")
            departments = [
                Department(name='تقنية المعلومات', description='قسم تطوير وصيانة الأنظمة'),
                Department(name='الموارد البشرية', description='إدارة شؤون الموظفين'),
                Department(name='المالية والمحاسبة', description='إدارة الشؤون المالية'),
                Department(name='التسويق والمبيعات', description='التسويق وخدمة العملاء'),
                Department(name='العمليات', description='إدارة العمليات التشغيلية')
            ]
            
            for dept in departments:
                db.session.add(dept)
            db.session.commit()
            print("✅ تم إنشاء الأقسام التجريبية")
        
        # إنشاء موظفين تجريبيين
        if Employee.query.count() == 0:
            print("👥 إنشاء الموظفين التجريبيين...")
            it_dept = Department.query.filter_by(name='تقنية المعلومات').first()
            hr_dept = Department.query.filter_by(name='الموارد البشرية').first()
            
            employees = [
                Employee(
                    full_name='أحمد محمد علي',
                    national_id='1234567890',
                    employee_id='EMP001',
                    job_title='مطور أول',
                    department_id=it_dept.id if it_dept else None,
                    appointment_date='2020-01-15',
                    years_of_service=4
                ),
                Employee(
                    full_name='فاطمة أحمد السالم',
                    national_id='0987654321',
                    employee_id='EMP002',
                    job_title='أخصائي موارد بشرية',
                    department_id=hr_dept.id if hr_dept else None,
                    appointment_date='2021-03-10',
                    years_of_service=3
                ),
                Employee(
                    full_name='محمد عبدالله الخالد',
                    national_id='1122334455',
                    employee_id='EMP003',
                    job_title='محلل أنظمة',
                    department_id=it_dept.id if it_dept else None,
                    appointment_date='2022-06-01',
                    years_of_service=2
                )
            ]
            
            for emp in employees:
                db.session.add(emp)
            db.session.commit()
            print("✅ تم إنشاء الموظفين التجريبيين")
        
        # إنشاء مشروع تجريبي
        if Project.query.count() == 0:
            print("📋 إنشاء المشاريع التجريبية...")
            manager = Employee.query.first()
            if manager:
                project = Project(
                    name='تطوير نظام إدارة الموظفين',
                    description='مشروع تطوير وتحسين نظام إدارة شؤون الموظفين',
                    start_date=date.today(),
                    end_date=date.today() + timedelta(days=90),
                    status='Active',
                    priority='High',
                    budget=50000,
                    manager_id=manager.id,
                    progress_percentage=25
                )
                db.session.add(project)
                db.session.commit()
                print("✅ تم إنشاء المشاريع التجريبية")
        
        # إنشاء تدريب تجريبي
        if Training.query.count() == 0:
            print("📚 إنشاء التدريبات التجريبية...")
            training = Training(
                title='دورة تطوير المهارات الإدارية',
                description='دورة تدريبية لتطوير المهارات الإدارية والقيادية',
                trainer_name='د. سارة أحمد',
                training_type='Internal',
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=14),
                duration_hours=40,
                max_participants=20,
                cost=5000,
                location='قاعة التدريب الرئيسية',
                status='Planned'
            )
            db.session.add(training)
            db.session.commit()
            print("✅ تم إنشاء التدريبات التجريبية")
        
        print("🎉 تم إعداد قاعدة البيانات بنجاح!")

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['uploads', 'backups', 'logs', 'exports']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 تم إنشاء مجلد: {directory}")

def display_info():
    """عرض معلومات النظام"""
    print("\n" + "="*60)
    print("🏢 نظام إدارة شؤون الموظفين المتقدم")
    print("="*60)
    print("📊 الميزات المتاحة:")
    print("   • إدارة الموظفين والأقسام")
    print("   • تتبع الحضور والانصراف")
    print("   • إدارة الإجازات والوثائق")
    print("   • تقييم الأداء والأهداف")
    print("   • إدارة المشاريع والمهام")
    print("   • نظام الرواتب والمكافآت")
    print("   • التدريب والتطوير المهني")
    print("   • التحليلات المتقدمة")
    print("   • النسخ الاحتياطية والأرشفة")
    print("\n🔐 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n🌐 رابط النظام:")
    print("   http://localhost:5000")
    print("="*60)

def main():
    """الوظيفة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة شؤون الموظفين المتقدم...")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد قاعدة البيانات
    setup_database()
    
    # عرض معلومات النظام
    display_info()
    
    # تشغيل التطبيق
    try:
        from app import app
        print("\n🔥 تشغيل الخادم...")
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
