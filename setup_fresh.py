#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعداد نظيف لقاعدة البيانات مع جميع الجداول الجديدة
"""

import os
from app import app, db

def setup_fresh_database():
    """إعداد قاعدة بيانات جديدة تماماً"""
    
    # حذف قاعدة البيانات القديمة إن وجدت
    if os.path.exists('employees.db'):
        os.remove('employees.db')
        print("تم حذف قاعدة البيانات القديمة")
    
    with app.app_context():
        # إنشاء جميع الجداول
        db.create_all()
        print("تم إنشاء جميع الجداول الجديدة")
        
        # إنشاء البيانات الأساسية
        from app import User, Role, Department, Employee
        import json
        
        # إنشاء الأدوار
        admin_role = Role(
            name='مدير النظام',
            description='صلاحيات كاملة لإدارة النظام',
            permissions=json.dumps([
                'view_employees', 'edit_employees', 'delete_employees',
                'manage_departments', 'manage_attendance', 'manage_leaves',
                'view_reports', 'manage_performance', 'manage_goals',
                'manage_kpis', 'manage_archives', 'manage_users'
            ], ensure_ascii=False)
        )
        db.session.add(admin_role)
        
        hr_role = Role(
            name='مدير الموارد البشرية',
            description='إدارة شؤون الموظفين والتقييمات',
            permissions=json.dumps([
                'view_employees', 'edit_employees', 'manage_departments',
                'manage_attendance', 'manage_leaves', 'view_reports',
                'manage_performance', 'manage_goals', 'manage_kpis'
            ], ensure_ascii=False)
        )
        db.session.add(hr_role)
        
        # حفظ الأدوار أولاً
        db.session.commit()
        
        # إنشاء المستخدمين
        admin_user = User(
            username='admin',
            password='admin123',
            email='<EMAIL>',
            full_name='مدير النظام',
            role_id=admin_role.id
        )
        db.session.add(admin_user)
        
        hr_user = User(
            username='hr_manager',
            password='hr123',
            email='<EMAIL>',
            full_name='مدير الموارد البشرية',
            role_id=hr_role.id
        )
        db.session.add(hr_user)
        
        # إنشاء الأقسام
        departments = [
            Department(name='الموارد البشرية', description='إدارة شؤون الموظفين'),
            Department(name='تقنية المعلومات', description='تطوير وصيانة الأنظمة'),
            Department(name='المالية والمحاسبة', description='إدارة الشؤون المالية'),
            Department(name='التسويق والمبيعات', description='تسويق المنتجات')
        ]
        
        for dept in departments:
            db.session.add(dept)
        
        # حفظ جميع التغييرات
        db.session.commit()
        
        print("✅ تم إنشاء البيانات الأساسية بنجاح!")
        print("\n" + "="*50)
        print("معلومات تسجيل الدخول:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("="*50)

if __name__ == '__main__':
    setup_fresh_database()
