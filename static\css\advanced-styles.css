/* تحسينات مرئية متقدمة لنظام إدارة شؤون الموظفين */

/* الألوان الأساسية */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    --info-gradient: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    --card-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --card-hover-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسينات عامة */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--card-hover-shadow);
}

.card-header {
    border: none;
    border-radius: 20px 20px 0 0 !important;
    padding: 20px;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 25px;
    padding: 12px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: var(--primary-gradient);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: var(--success-gradient);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-warning {
    background: var(--warning-gradient);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-danger {
    background: var(--danger-gradient);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-info {
    background: var(--info-gradient);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

/* تحسينات الجداول */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

/* تحسينات الشارات */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 500;
    font-size: 0.85rem;
}

/* تحسينات شريط التقدم */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #f8f9fa;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-bar {
    border-radius: 15px;
    transition: all 0.5s ease;
    background: var(--success-gradient);
}

/* تحسينات الإشعارات */
.notification-item {
    border-radius: 15px;
    border-left: 5px solid #667eea;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.notification-item:hover {
    transform: translateX(-10px);
    box-shadow: var(--card-hover-shadow);
}

.notification-item.unread {
    background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
    border-left-color: #ff6b6b;
}

/* تحسينات الأيقونات */
.icon-large {
    font-size: 3rem;
    margin-bottom: 20px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات النافذة المنبثقة */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-radius: 20px 20px 0 0;
    background: var(--primary-gradient);
    color: white;
    border: none;
}

/* تحسينات شريط التنقل */
.navbar {
    backdrop-filter: blur(10px);
    border-radius: 0 0 20px 20px;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .metric-card {
        margin-bottom: 15px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background: white;
    }
}

/* تحسينات إضافية للتفاعل */
.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.clickable:hover {
    transform: scale(1.05);
}

/* تحسينات النصوص */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات الحالات */
.status-active {
    color: #28a745;
    font-weight: 600;
}

.status-inactive {
    color: #dc3545;
    font-weight: 600;
}

.status-pending {
    color: #ffc107;
    font-weight: 600;
}

/* تحسينات التقييمات */
.rating-display {
    display: flex;
    align-items: center;
    gap: 5px;
}

.rating-star {
    color: #ffc107;
    font-size: 1.2rem;
}

.rating-value {
    font-weight: 600;
    color: #495057;
}

/* تحسينات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* تحسينات التنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(232, 62, 140, 0.1) 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(111, 66, 193, 0.1) 100%);
    color: #0c5460;
}
