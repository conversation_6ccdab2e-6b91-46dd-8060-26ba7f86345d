// وظائف متقدمة لنظام إدارة شؤون الموظفين

// إدارة الإشعارات
class NotificationManager {
    constructor() {
        this.checkInterval = 60000; // دقيقة واحدة
        this.init();
    }

    init() {
        this.startAutoCheck();
        this.bindEvents();
    }

    startAutoCheck() {
        setInterval(() => {
            this.checkNewNotifications();
        }, this.checkInterval);
    }

    async checkNewNotifications() {
        try {
            const response = await fetch('/api/notifications/count');
            const data = await response.json();
            
            if (data.unread_count > 0) {
                this.updateNotificationBadge(data.unread_count);
                this.showNotificationToast(data.latest_notification);
            }
        } catch (error) {
            console.error('خطأ في فحص الإشعارات:', error);
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'inline' : 'none';
        }
    }

    showNotificationToast(notification) {
        if (!notification) return;

        const toast = document.createElement('div');
        toast.className = 'toast position-fixed top-0 end-0 m-3';
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-bell text-primary me-2"></i>
                <strong class="me-auto">${notification.title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${notification.message}
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // إزالة التوست بعد إخفائه
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    bindEvents() {
        // تحديد جميع الإشعارات كمقروءة
        const markAllReadBtn = document.querySelector('#markAllRead');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', () => {
                this.markAllAsRead();
            });
        }
    }

    async markAllAsRead() {
        try {
            const response = await fetch('/api/notifications/mark_all_read', {
                method: 'POST'
            });
            
            if (response.ok) {
                location.reload();
            }
        } catch (error) {
            console.error('خطأ في تحديد الإشعارات كمقروءة:', error);
        }
    }
}

// إدارة الرسوم البيانية
class ChartManager {
    constructor() {
        this.charts = {};
        this.colors = {
            primary: '#667eea',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545',
            info: '#17a2b8'
        };
    }

    createAttendanceChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        this.charts[canvasId] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'نسبة الحضور',
                    data: data.values,
                    borderColor: this.colors.primary,
                    backgroundColor: this.colors.primary + '20',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    createPerformanceChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        this.charts[canvasId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['ممتاز (5)', 'جيد جداً (4)', 'جيد (3)', 'مقبول (2)', 'ضعيف (1)'],
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        this.colors.success,
                        this.colors.info,
                        this.colors.warning,
                        '#fd7e14',
                        this.colors.danger
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    updateChart(chartId, newData) {
        const chart = this.charts[chartId];
        if (chart) {
            chart.data.datasets[0].data = newData;
            chart.update();
        }
    }
}

// إدارة البيانات في الوقت الفعلي
class RealTimeDataManager {
    constructor() {
        this.updateInterval = 30000; // 30 ثانية
        this.init();
    }

    init() {
        this.startRealTimeUpdates();
    }

    startRealTimeUpdates() {
        setInterval(() => {
            this.updateDashboardData();
        }, this.updateInterval);
    }

    async updateDashboardData() {
        try {
            const response = await fetch('/dashboard_data');
            const data = await response.json();
            
            // تحديث المقاييس
            this.updateMetrics(data);
            
            // تحديث الرسوم البيانية إذا لزم الأمر
            this.updateCharts(data);
            
        } catch (error) {
            console.error('خطأ في تحديث البيانات:', error);
        }
    }

    updateMetrics(data) {
        const metrics = {
            'totalEmployees': data.total_employees,
            'presentToday': data.present_today,
            'pendingLeaves': data.pending_leaves,
            'avgPerformance': data.avg_rating,
            'activeGoals': data.active_goals,
            'unreadNotifications': data.unread_notifications
        };

        Object.entries(metrics).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                // تأثير الانتقال السلس
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.textContent = value;
                    element.style.transform = 'scale(1)';
                }, 150);
            }
        });
    }

    updateCharts(data) {
        // تحديث الرسوم البيانية حسب الحاجة
        // يمكن إضافة منطق تحديث الرسوم البيانية هنا
    }
}

// إدارة البحث المتقدم
class AdvancedSearch {
    constructor() {
        this.init();
    }

    init() {
        this.bindSearchEvents();
        this.initAutoComplete();
    }

    bindSearchEvents() {
        // البحث الفوري
        const searchInputs = document.querySelectorAll('.instant-search');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.performInstantSearch(e.target);
                }, 300);
            });
        });
    }

    async performInstantSearch(input) {
        const query = input.value;
        const searchType = input.dataset.searchType;
        
        if (query.length < 2) return;

        try {
            const response = await fetch(`/api/search?type=${searchType}&query=${encodeURIComponent(query)}`);
            const results = await response.json();
            
            this.displaySearchResults(input, results);
        } catch (error) {
            console.error('خطأ في البحث:', error);
        }
    }

    displaySearchResults(input, results) {
        // إنشاء قائمة النتائج
        let resultsContainer = input.parentElement.querySelector('.search-results');
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.className = 'search-results position-absolute bg-white border rounded shadow-sm';
            resultsContainer.style.top = '100%';
            resultsContainer.style.left = '0';
            resultsContainer.style.right = '0';
            resultsContainer.style.zIndex = '1000';
            input.parentElement.style.position = 'relative';
            input.parentElement.appendChild(resultsContainer);
        }

        resultsContainer.innerHTML = '';
        
        results.forEach(result => {
            const item = document.createElement('div');
            item.className = 'p-2 border-bottom cursor-pointer';
            item.innerHTML = `
                <div class="fw-bold">${result.title}</div>
                <small class="text-muted">${result.description}</small>
            `;
            
            item.addEventListener('click', () => {
                input.value = result.title;
                resultsContainer.style.display = 'none';
            });
            
            resultsContainer.appendChild(item);
        });

        resultsContainer.style.display = results.length > 0 ? 'block' : 'none';
    }

    initAutoComplete() {
        // إخفاء نتائج البحث عند النقر خارجها
        document.addEventListener('click', (e) => {
            const searchResults = document.querySelectorAll('.search-results');
            searchResults.forEach(container => {
                if (!container.contains(e.target) && !container.previousElementSibling.contains(e.target)) {
                    container.style.display = 'none';
                }
            });
        });
    }
}

// إدارة التصدير والطباعة
class ExportManager {
    static exportToExcel(tableId, filename) {
        const table = document.getElementById(tableId);
        if (!table) return;

        // تحويل الجدول إلى CSV
        let csv = '';
        const rows = table.querySelectorAll('tr');
        
        rows.forEach(row => {
            const cols = row.querySelectorAll('td, th');
            const rowData = Array.from(cols).map(col => {
                return '"' + col.textContent.trim().replace(/"/g, '""') + '"';
            });
            csv += rowData.join(',') + '\n';
        });

        // تحميل الملف
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename + '.csv';
        link.click();
    }

    static printReport(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>تقرير</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .no-print { display: none !important; }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.print();
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة مدير الإشعارات
    const notificationManager = new NotificationManager();
    
    // تهيئة مدير البيانات في الوقت الفعلي
    const realTimeManager = new RealTimeDataManager();
    
    // تهيئة البحث المتقدم
    const advancedSearch = new AdvancedSearch();
    
    // تهيئة مدير الرسوم البيانية
    const chartManager = new ChartManager();
    
    // إضافة تأثيرات بصرية
    addVisualEffects();
});

// إضافة تأثيرات بصرية
function addVisualEffects() {
    // تأثير التحميل للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // تأثير التمرير السلس
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // تأثير التحميل للجداول
    const tables = document.querySelectorAll('.table tbody tr');
    tables.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// وظائف مساعدة
const Utils = {
    // تنسيق التاريخ
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    },

    // تنسيق الوقت
    formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('ar-SA');
    },

    // تنسيق الأرقام
    formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    },

    // حساب النسبة المئوية
    calculatePercentage(value, total) {
        if (total === 0) return 0;
        return Math.round((value / total) * 100);
    },

    // تحديد لون التقييم
    getRatingColor(rating) {
        if (rating >= 4.5) return 'success';
        if (rating >= 3.5) return 'info';
        if (rating >= 2.5) return 'warning';
        return 'danger';
    },

    // تحديد لون التقدم
    getProgressColor(percentage) {
        if (percentage >= 90) return 'success';
        if (percentage >= 70) return 'info';
        if (percentage >= 50) return 'warning';
        return 'danger';
    },

    // عرض رسالة تأكيد
    showConfirmDialog(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },

    // عرض رسالة نجاح
    showSuccessMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    },

    // عرض رسالة خطأ
    showErrorMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
};

// تصدير الكلاسات للاستخدام العام
window.NotificationManager = NotificationManager;
window.ChartManager = ChartManager;
window.RealTimeDataManager = RealTimeDataManager;
window.AdvancedSearch = AdvancedSearch;
window.ExportManager = ExportManager;
window.Utils = Utils;
