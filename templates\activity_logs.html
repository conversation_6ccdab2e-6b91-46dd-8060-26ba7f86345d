<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل العمليات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }
        .activity-create {
            background-color: #d4edda;
            color: #155724;
        }
        .activity-update {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .activity-delete {
            background-color: #f8d7da;
            color: #721c24;
        }
        .activity-login {
            background-color: #fff3cd;
            color: #856404;
        }
        .log-details {
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-history me-2"></i>سجل العمليات</h2>
                <p class="text-muted">تتبع جميع العمليات والأنشطة في النظام</p>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-plus-circle fa-2x text-success mb-2"></i>
                        <h4>{{ logs|selectattr('action', 'match', '.*إضافة.*')|list|length }}</h4>
                        <small class="text-muted">عمليات إضافة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-edit fa-2x text-info mb-2"></i>
                        <h4>{{ logs|selectattr('action', 'match', '.*تعديل.*')|list|length }}</h4>
                        <small class="text-muted">عمليات تعديل</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-trash fa-2x text-danger mb-2"></i>
                        <h4>{{ logs|selectattr('action', 'match', '.*حذف.*')|list|length }}</h4>
                        <small class="text-muted">عمليات حذف</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-sign-in-alt fa-2x text-warning mb-2"></i>
                        <h4>{{ logs|selectattr('action', 'match', '.*تسجيل.*')|list|length }}</h4>
                        <small class="text-muted">عمليات دخول</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول سجل العمليات -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>آخر 100 عملية</h5>
            </div>
            <div class="card-body">
                {% if logs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>النشاط</th>
                                <th>المستخدم</th>
                                <th>الجدول</th>
                                <th>معرف السجل</th>
                                <th>التاريخ والوقت</th>
                                <th>عنوان IP</th>
                                <th>التفاصيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon 
                                            {% if 'إضافة' in log.action %}activity-create
                                            {% elif 'تعديل' in log.action %}activity-update
                                            {% elif 'حذف' in log.action %}activity-delete
                                            {% else %}activity-login{% endif %} me-2">
                                            {% if 'إضافة' in log.action %}
                                                <i class="fas fa-plus"></i>
                                            {% elif 'تعديل' in log.action %}
                                                <i class="fas fa-edit"></i>
                                            {% elif 'حذف' in log.action %}
                                                <i class="fas fa-trash"></i>
                                            {% else %}
                                                <i class="fas fa-info"></i>
                                            {% endif %}
                                        </div>
                                        <span>{{ log.action }}</span>
                                    </div>
                                </td>
                                <td>{{ log.user.username if log.user else 'غير محدد' }}</td>
                                <td>
                                    {% if log.table_name %}
                                        <span class="badge bg-secondary">{{ log.table_name }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.record_id or '-' }}</td>
                                <td>
                                    <div>{{ log.timestamp.strftime('%Y-%m-%d') }}</div>
                                    <small class="text-muted">{{ log.timestamp.strftime('%H:%M:%S') }}</small>
                                </td>
                                <td>
                                    <small class="text-muted">{{ log.ip_address or '-' }}</small>
                                </td>
                                <td>
                                    {% if log.old_values or log.new_values %}
                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                            onclick="showDetails('{{ log.old_values|e }}', '{{ log.new_values|e }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عمليات مسجلة</h5>
                    <p class="text-muted">ستظهر العمليات هنا عند تنفيذها</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل العملية -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل العملية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>القيم القديمة:</h6>
                            <pre id="oldValues" class="bg-light p-3 rounded"></pre>
                        </div>
                        <div class="col-md-6">
                            <h6>القيم الجديدة:</h6>
                            <pre id="newValues" class="bg-light p-3 rounded"></pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showDetails(oldValues, newValues) {
            document.getElementById('oldValues').textContent = oldValues || 'لا توجد قيم قديمة';
            document.getElementById('newValues').textContent = newValues || 'لا توجد قيم جديدة';
            new bootstrap.Modal(document.getElementById('detailsModal')).show();
        }
    </script>
</body>
</html>
