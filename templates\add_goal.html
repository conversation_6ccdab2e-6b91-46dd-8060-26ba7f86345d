<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة هدف جديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .section-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/manage_goals">
                    <i class="fas fa-arrow-right me-1"></i>العودة للأهداف
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-plus me-2"></i>إضافة هدف جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- معلومات أساسية -->
                            <div class="section-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات الهدف</h5>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">الموظف <span class="text-danger">*</span></label>
                                    <select class="form-select" name="employee_id" required>
                                        <option value="">اختر الموظف</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}">{{ employee.full_name }} - {{ employee.employee_id }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">عنوان الهدف <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="title" required 
                                           placeholder="أدخل عنوان الهدف...">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">وصف الهدف</label>
                                    <textarea class="form-control" name="description" rows="3" 
                                              placeholder="أدخل وصفاً تفصيلياً للهدف..."></textarea>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label">التاريخ المستهدف <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" name="target_date" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">الأولوية</label>
                                    <select class="form-select" name="priority">
                                        <option value="Medium" selected>متوسطة</option>
                                        <option value="High">عالية</option>
                                        <option value="Low">منخفضة</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">نسبة التقدم (%)</label>
                                    <input type="number" class="form-control" name="progress_percentage" 
                                           min="0" max="100" value="0">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" 
                                              placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_goals') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-1"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ الهدف
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين التاريخ الافتراضي لشهر من الآن
        document.addEventListener('DOMContentLoaded', function() {
            const targetDateInput = document.querySelector('input[name="target_date"]');
            const today = new Date();
            const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
            targetDateInput.value = nextMonth.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
