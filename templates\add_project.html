<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مشروع جديد - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .form-label { font-weight: 600; }
        .btn-primary { background: linear-gradient(45deg, #007bff, #0056b3); border: none; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-plus"></i> إضافة مشروع جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">اسم المشروع *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="manager_id" class="form-label">مدير المشروع *</label>
                                    <select class="form-select" id="manager_id" name="manager_id" required>
                                        <option value="">اختر مدير المشروع</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}">{{ employee.full_name }} - {{ employee.job_title }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المشروع</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية *</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="end_date" class="form-label">تاريخ النهاية المتوقع</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="status" class="form-label">حالة المشروع</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="Active">نشط</option>
                                        <option value="On Hold">معلق</option>
                                        <option value="Completed">مكتمل</option>
                                        <option value="Cancelled">ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="Low">منخفضة</option>
                                        <option value="Medium" selected>متوسطة</option>
                                        <option value="High">عالية</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="department_id" class="form-label">القسم</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">اختر القسم</option>
                                        {% for dept in departments %}
                                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="budget" class="form-label">الميزانية المقدرة</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="budget" name="budget" step="0.01" min="0">
                                    <span class="input-group-text">ريال</span>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_projects') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ المشروع
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين تاريخ اليوم كتاريخ افتراضي للبداية
        document.getElementById('start_date').value = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>
