<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة دور جديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .section-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .permission-group {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .permission-group h6 {
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/manage_roles">
                    <i class="fas fa-arrow-right me-1"></i>العودة للأدوار
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-plus me-2"></i>إضافة دور جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- معلومات أساسية -->
                            <div class="section-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات الدور</h5>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">اسم الدور <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" required 
                                           placeholder="مثال: مدير الموارد البشرية">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">وصف الدور</label>
                                    <input type="text" class="form-control" name="description" 
                                           placeholder="وصف مختصر للدور ومسؤولياته">
                                </div>
                            </div>

                            <!-- الصلاحيات -->
                            <div class="section-header">
                                <h5><i class="fas fa-key me-2"></i>الصلاحيات</h5>
                            </div>

                            <!-- صلاحيات الموظفين -->
                            <div class="permission-group">
                                <h6><i class="fas fa-users me-2"></i>إدارة الموظفين</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_view_employees" id="viewEmployees">
                                            <label class="form-check-label" for="viewEmployees">عرض الموظفين</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_edit_employees" id="editEmployees">
                                            <label class="form-check-label" for="editEmployees">تعديل الموظفين</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_delete_employees" id="deleteEmployees">
                                            <label class="form-check-label" for="deleteEmployees">حذف الموظفين</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- صلاحيات الأقسام والحضور -->
                            <div class="permission-group">
                                <h6><i class="fas fa-building me-2"></i>الأقسام والحضور</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_departments" id="manageDepartments">
                                            <label class="form-check-label" for="manageDepartments">إدارة الأقسام</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_attendance" id="manageAttendance">
                                            <label class="form-check-label" for="manageAttendance">إدارة الحضور</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_leaves" id="manageLeaves">
                                            <label class="form-check-label" for="manageLeaves">إدارة الإجازات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- صلاحيات التقييم والأهداف -->
                            <div class="permission-group">
                                <h6><i class="fas fa-star me-2"></i>التقييم والأهداف</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_performance" id="managePerformance">
                                            <label class="form-check-label" for="managePerformance">إدارة التقييمات</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_goals" id="manageGoals">
                                            <label class="form-check-label" for="manageGoals">إدارة الأهداف</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_kpis" id="manageKpis">
                                            <label class="form-check-label" for="manageKpis">إدارة المؤشرات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- صلاحيات النظام -->
                            <div class="permission-group">
                                <h6><i class="fas fa-cog me-2"></i>إدارة النظام</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_view_reports" id="viewReports">
                                            <label class="form-check-label" for="viewReports">عرض التقارير</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_archives" id="manageArchives">
                                            <label class="form-check-label" for="manageArchives">إدارة الأرشيف</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="can_manage_users" id="manageUsers">
                                            <label class="form-check-label" for="manageUsers">إدارة المستخدمين</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_roles') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-1"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ الدور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف هذا الدور؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(roleId) {
            document.getElementById('deleteForm').action = '/delete_role/' + roleId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // تحديد/إلغاء تحديد جميع الصلاحيات في مجموعة
        document.querySelectorAll('.permission-group h6').forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const group = this.parentElement;
                const checkboxes = group.querySelectorAll('input[type="checkbox"]');
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                
                checkboxes.forEach(cb => {
                    cb.checked = !allChecked;
                });
            });
        });
    </script>
</body>
</html>
