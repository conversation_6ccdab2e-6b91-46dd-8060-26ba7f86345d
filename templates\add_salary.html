<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة راتب جديد - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .form-label { font-weight: 600; }
        .btn-primary { background: linear-gradient(45deg, #007bff, #0056b3); border: none; }
        .salary-calculation { background: #f8f9fa; border-radius: 8px; padding: 15px; }
        .net-salary { font-size: 1.5em; font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-money-bill-wave"></i> إضافة راتب جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="salaryForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="employee_id" class="form-label">الموظف *</label>
                                    <select class="form-select" id="employee_id" name="employee_id" required>
                                        <option value="">اختر الموظف</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}" data-name="{{ employee.full_name }}">
                                            {{ employee.full_name }} - {{ employee.employee_id }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="pay_period_start" class="form-label">بداية فترة الراتب *</label>
                                    <input type="date" class="form-control" id="pay_period_start" name="pay_period_start" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="pay_period_end" class="form-label">نهاية فترة الراتب *</label>
                                    <input type="date" class="form-control" id="pay_period_end" name="pay_period_end" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-success"><i class="fas fa-plus-circle"></i> المبالغ الإضافية</h5>
                                    
                                    <div class="mb-3">
                                        <label for="basic_salary" class="form-label">الراتب الأساسي *</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="basic_salary" name="basic_salary" 
                                                   step="0.01" min="0" required oninput="calculateSalary()">
                                            <span class="input-group-text">ريال</span>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="allowances" class="form-label">البدلات</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="allowances" name="allowances" 
                                                   step="0.01" min="0" value="0" oninput="calculateSalary()">
                                            <span class="input-group-text">ريال</span>
                                        </div>
                                        <small class="text-muted">بدل سكن، مواصلات، طعام، إلخ</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-6 mb-3">
                                            <label for="overtime_hours" class="form-label">ساعات إضافية</label>
                                            <input type="number" class="form-control" id="overtime_hours" name="overtime_hours" 
                                                   step="0.5" min="0" value="0" oninput="calculateSalary()">
                                        </div>
                                        <div class="col-6 mb-3">
                                            <label for="overtime_rate" class="form-label">سعر الساعة الإضافية</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="overtime_rate" name="overtime_rate" 
                                                       step="0.01" min="0" value="0" oninput="calculateSalary()">
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="bonus" class="form-label">المكافآت والحوافز</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="bonus" name="bonus" 
                                                   step="0.01" min="0" value="0" oninput="calculateSalary()">
                                            <span class="input-group-text">ريال</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5 class="text-danger"><i class="fas fa-minus-circle"></i> الخصومات</h5>
                                    
                                    <div class="mb-3">
                                        <label for="deductions" class="form-label">إجمالي الخصومات</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="deductions" name="deductions" 
                                                   step="0.01" min="0" value="0" oninput="calculateSalary()">
                                            <span class="input-group-text">ريال</span>
                                        </div>
                                        <small class="text-muted">تأمينات، ضرائب، قروض، غياب، إلخ</small>
                                    </div>

                                    <!-- حاسبة الراتب -->
                                    <div class="salary-calculation">
                                        <h6><i class="fas fa-calculator"></i> حاسبة الراتب</h6>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <small class="text-muted">الراتب الإجمالي</small>
                                                <div id="grossSalary" class="fw-bold">0 ريال</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">إجمالي الخصومات</small>
                                                <div id="totalDeductions" class="fw-bold text-danger">0 ريال</div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="text-center">
                                            <small class="text-muted">الراتب الصافي</small>
                                            <div id="netSalary" class="net-salary">0 ريال</div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <label for="payment_date" class="form-label">تاريخ الدفع</label>
                                        <input type="date" class="form-control" id="payment_date" name="payment_date">
                                    </div>

                                    <div class="mt-3">
                                        <label for="status" class="form-label">حالة الراتب</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="Pending" selected>معلق</option>
                                            <option value="Paid">مدفوع</option>
                                            <option value="Cancelled">ملغي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2" 
                                          placeholder="أي ملاحظات إضافية حول الراتب..."></textarea>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>تنبيه:</strong> تأكد من صحة جميع المبالغ قبل الحفظ. يمكن تعديل الراتب لاحقاً إذا لزم الأمر.
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_salaries') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة
                                </a>
                                <div>
                                    <button type="button" class="btn btn-info me-2" onclick="autoCalculate()">
                                        <i class="fas fa-magic"></i> حساب تلقائي
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ الراتب
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين تواريخ افتراضية (الشهر الحالي)
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        document.getElementById('pay_period_start').value = firstDay.toISOString().split('T')[0];
        document.getElementById('pay_period_end').value = lastDay.toISOString().split('T')[0];

        function calculateSalary() {
            const basicSalary = parseFloat(document.getElementById('basic_salary').value) || 0;
            const allowances = parseFloat(document.getElementById('allowances').value) || 0;
            const overtimeHours = parseFloat(document.getElementById('overtime_hours').value) || 0;
            const overtimeRate = parseFloat(document.getElementById('overtime_rate').value) || 0;
            const bonus = parseFloat(document.getElementById('bonus').value) || 0;
            const deductions = parseFloat(document.getElementById('deductions').value) || 0;

            const overtimePay = overtimeHours * overtimeRate;
            const grossSalary = basicSalary + allowances + overtimePay + bonus;
            const netSalary = grossSalary - deductions;

            document.getElementById('grossSalary').textContent = grossSalary.toLocaleString('ar-SA') + ' ريال';
            document.getElementById('totalDeductions').textContent = deductions.toLocaleString('ar-SA') + ' ريال';
            document.getElementById('netSalary').textContent = netSalary.toLocaleString('ar-SA') + ' ريال';

            // تغيير لون الراتب الصافي
            const netSalaryElement = document.getElementById('netSalary');
            if (netSalary < 0) {
                netSalaryElement.className = 'net-salary text-danger';
            } else {
                netSalaryElement.className = 'net-salary text-success';
            }
        }

        function autoCalculate() {
            const employeeId = document.getElementById('employee_id').value;
            if (!employeeId) {
                alert('يرجى اختيار الموظف أولاً');
                return;
            }

            // استدعاء API للحساب التلقائي
            fetch(`/calculate_salary/${employeeId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('basic_salary').value = data.suggested_basic_salary;
                    document.getElementById('allowances').value = data.suggested_allowances;
                    
                    // حساب الساعات الإضافية بناءً على أيام الحضور
                    const extraDays = Math.max(0, data.attendance_days - 22); // أكثر من 22 يوم عمل
                    document.getElementById('overtime_hours').value = extraDays * 8;
                    document.getElementById('overtime_rate').value = 50; // 50 ريال للساعة
                    
                    calculateSalary();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الحساب التلقائي');
                });
        }

        // حساب تلقائي عند تغيير أي قيمة
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', calculateSalary);
        });

        // حساب أولي
        calculateSalary();
    </script>
</body>
</html>
