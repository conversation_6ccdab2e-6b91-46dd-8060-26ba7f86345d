<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مهمة جديدة - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .form-label { font-weight: 600; }
        .btn-primary { background: linear-gradient(45deg, #007bff, #0056b3); border: none; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-plus"></i> إضافة مهمة جديدة</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="title" class="form-label">عنوان المهمة *</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="Low">منخفضة</option>
                                        <option value="Medium" selected>متوسطة</option>
                                        <option value="High">عالية</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المهمة</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="project_id" class="form-label">المشروع</label>
                                    <select class="form-select" id="project_id" name="project_id">
                                        <option value="">اختر المشروع (اختياري)</option>
                                        {% for project in projects %}
                                        <option value="{{ project.id }}">{{ project.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="assigned_to" class="form-label">المكلف بالمهمة *</label>
                                    <select class="form-select" id="assigned_to" name="assigned_to" required>
                                        <option value="">اختر الموظف</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}">{{ employee.full_name }} - {{ employee.job_title }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="status" class="form-label">حالة المهمة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="To Do" selected>للتنفيذ</option>
                                        <option value="In Progress">قيد التنفيذ</option>
                                        <option value="Review">للمراجعة</option>
                                        <option value="Done">مكتملة</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" class="form-control" id="due_date" name="due_date">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="estimated_hours" class="form-label">الساعات المقدرة</label>
                                    <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" 
                                           step="0.5" min="0" placeholder="مثال: 8">
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>ملاحظة:</strong> سيتم إرسال إشعار للموظف المكلف بالمهمة تلقائياً.
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_tasks') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ المهمة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين تاريخ الغد كتاريخ افتراضي للاستحقاق
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 7); // أسبوع من اليوم
        document.getElementById('due_date').value = tomorrow.toISOString().split('T')[0];

        // تغيير لون الأولوية
        document.getElementById('priority').addEventListener('change', function() {
            const priority = this.value;
            this.className = 'form-select';
            if (priority === 'High') {
                this.classList.add('border-danger');
            } else if (priority === 'Medium') {
                this.classList.add('border-warning');
            } else {
                this.classList.add('border-success');
            }
        });
    </script>
</body>
</html>
