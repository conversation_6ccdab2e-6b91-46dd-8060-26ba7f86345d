<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة تدريب جديد - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .form-label { font-weight: 600; }
        .btn-primary { background: linear-gradient(45deg, #007bff, #0056b3); border: none; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><i class="fas fa-graduation-cap"></i> إضافة تدريب جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="title" class="form-label">عنوان التدريب *</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="training_type" class="form-label">نوع التدريب</label>
                                    <select class="form-select" id="training_type" name="training_type">
                                        <option value="Internal" selected>داخلي</option>
                                        <option value="External">خارجي</option>
                                        <option value="Online">إلكتروني</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف التدريب</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="trainer_name" class="form-label">اسم المدرب</label>
                                    <input type="text" class="form-control" id="trainer_name" name="trainer_name">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="location" class="form-label">مكان التدريب</label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           placeholder="مثال: قاعة التدريب الرئيسية">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="start_date" class="form-label">تاريخ البداية *</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="end_date" class="form-label">تاريخ النهاية *</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" required>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="duration_hours" class="form-label">عدد الساعات</label>
                                    <input type="number" class="form-control" id="duration_hours" name="duration_hours" 
                                           min="1" placeholder="مثال: 16">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="max_participants" class="form-label">الحد الأقصى للمشاركين</label>
                                    <input type="number" class="form-control" id="max_participants" name="max_participants" 
                                           min="1" placeholder="مثال: 20">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="cost" class="form-label">التكلفة</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="cost" name="cost" 
                                               step="0.01" min="0" placeholder="0">
                                        <span class="input-group-text">ريال</span>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="status" class="form-label">حالة التدريب</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="Planned" selected>مخطط</option>
                                        <option value="Ongoing">جاري</option>
                                        <option value="Completed">مكتمل</option>
                                        <option value="Cancelled">ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">نوع التدريب</label>
                                    <div class="d-flex gap-3 mt-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="category" id="technical" value="technical">
                                            <label class="form-check-label" for="technical">تقني</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="category" id="management" value="management">
                                            <label class="form-check-label" for="management">إداري</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="category" id="soft_skills" value="soft_skills" checked>
                                            <label class="form-check-label" for="soft_skills">مهارات شخصية</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="card bg-light mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> معلومات إضافية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="certificate" name="certificate">
                                                <label class="form-check-label" for="certificate">
                                                    يتضمن شهادة إتمام
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="mandatory" name="mandatory">
                                                <label class="form-check-label" for="mandatory">
                                                    تدريب إجباري
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="materials" name="materials">
                                                <label class="form-check-label" for="materials">
                                                    يتضمن مواد تدريبية
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="evaluation" name="evaluation">
                                                <label class="form-check-label" for="evaluation">
                                                    يتضمن تقييم نهائي
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-lightbulb"></i>
                                <strong>نصيحة:</strong> يمكنك إضافة المشاركين بعد إنشاء التدريب من صفحة إدارة التدريبات.
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_trainings') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة
                                </a>
                                <div>
                                    <button type="submit" class="btn btn-outline-primary me-2" name="action" value="draft">
                                        <i class="fas fa-save"></i> حفظ كمسودة
                                    </button>
                                    <button type="submit" class="btn btn-primary" name="action" value="publish">
                                        <i class="fas fa-check"></i> حفظ ونشر
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين تاريخ الغد كتاريخ افتراضي للبداية
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('start_date').value = tomorrow.toISOString().split('T')[0];

        // تعيين تاريخ نهاية افتراضي (أسبوع من البداية)
        const endDate = new Date(tomorrow);
        endDate.setDate(endDate.getDate() + 6);
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // تحديث المكان حسب نوع التدريب
        document.getElementById('training_type').addEventListener('change', function() {
            const locationField = document.getElementById('location');
            if (this.value === 'Online') {
                locationField.placeholder = 'رابط المنصة الإلكترونية';
                locationField.value = '';
            } else if (this.value === 'External') {
                locationField.placeholder = 'عنوان مكان التدريب الخارجي';
            } else {
                locationField.placeholder = 'قاعة التدريب الرئيسية';
            }
        });

        // حساب عدد الساعات تلقائياً
        function calculateHours() {
            const startDate = new Date(document.getElementById('start_date').value);
            const endDate = new Date(document.getElementById('end_date').value);
            
            if (startDate && endDate && endDate > startDate) {
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                const estimatedHours = diffDays * 6; // 6 ساعات في اليوم
                
                if (!document.getElementById('duration_hours').value) {
                    document.getElementById('duration_hours').value = estimatedHours;
                }
            }
        }

        document.getElementById('start_date').addEventListener('change', calculateHours);
        document.getElementById('end_date').addEventListener('change', calculateHours);
    </script>
</body>
</html>
