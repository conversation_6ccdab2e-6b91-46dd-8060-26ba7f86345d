<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحليلات المتقدمة - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; margin-bottom: 20px; }
        .metric-card { text-align: center; padding: 20px; }
        .metric-value { font-size: 2.5em; font-weight: bold; }
        .chart-container { position: relative; height: 400px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link active" href="{{ url_for('advanced_analytics') }}"><i class="fas fa-chart-bar"></i> التحليلات</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-chart-line"></i> التحليلات المتقدمة ولوحة المعلومات</h2>
                <p class="text-muted">تحليلات شاملة لأداء النظام والموظفين</p>
            </div>
        </div>

        <!-- المؤشرات الرئيسية -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-gradient-primary text-white metric-card">
                    <div class="metric-value">{{ performance_stats.total_reviews or 0 }}</div>
                    <div>إجمالي التقييمات</div>
                    <small>متوسط التقييم: {{ "%.1f"|format(performance_stats.avg_rating or 0) }}/5</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-gradient-success text-white metric-card">
                    <div class="metric-value">{{ attendance_stats|sum(attribute='count') or 0 }}</div>
                    <div>سجلات الحضور (الشهر الحالي)</div>
                    <small>معدل الحضور: 85%</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-gradient-warning text-white metric-card">
                    <div class="metric-value">{{ project_stats|sum(attribute='count') or 0 }}</div>
                    <div>إجمالي المشاريع</div>
                    <small>نشطة: {{ project_stats|selectattr('status', 'equalto', 'Active')|sum(attribute='count') or 0 }}</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-gradient-info text-white metric-card">
                    <div class="metric-value">{{ training_stats|sum(attribute='count') or 0 }}</div>
                    <div>برامج التدريب</div>
                    <small>مكتملة: {{ training_stats|selectattr('status', 'equalto', 'Completed')|sum(attribute='count') or 0 }}</small>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> توزيع حالات الحضور</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="attendanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> حالات المشاريع</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="projectChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-graduation-cap"></i> إحصائيات التدريب</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="trainingChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-star"></i> توزيع تقييمات الأداء</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جداول تفصيلية -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table"></i> ملخص الإحصائيات التفصيلية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>إحصائيات الحضور</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الحالة</th>
                                                <th>العدد</th>
                                                <th>النسبة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for stat in attendance_stats %}
                                            <tr>
                                                <td>{{ stat.status }}</td>
                                                <td>{{ stat.count }}</td>
                                                <td>
                                                    {% set total = attendance_stats|sum(attribute='count') %}
                                                    {{ "%.1f"|format((stat.count / total * 100) if total > 0 else 0) }}%
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>إحصائيات المشاريع</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الحالة</th>
                                                <th>العدد</th>
                                                <th>النسبة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for stat in project_stats %}
                                            <tr>
                                                <td>{{ stat.status }}</td>
                                                <td>{{ stat.count }}</td>
                                                <td>
                                                    {% set total = project_stats|sum(attribute='count') %}
                                                    {{ "%.1f"|format((stat.count / total * 100) if total > 0 else 0) }}%
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التصدير -->
        <div class="row">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                    </button>
                    <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> تصدير إلى PDF
                    </button>
                    <button type="button" class="btn btn-info" onclick="refreshData()">
                        <i class="fas fa-sync"></i> تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // بيانات الحضور
        const attendanceData = {
            labels: [{% for stat in attendance_stats %}'{{ stat.status }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for stat in attendance_stats %}{{ stat.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#17a2b8'],
                borderWidth: 2
            }]
        };

        // بيانات المشاريع
        const projectData = {
            labels: [{% for stat in project_stats %}'{{ stat.status }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for stat in project_stats %}{{ stat.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545'],
                borderWidth: 1
            }]
        };

        // بيانات التدريب
        const trainingData = {
            labels: [{% for stat in training_stats %}'{{ stat.status }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for stat in training_stats %}{{ stat.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#17a2b8', '#ffc107', '#28a745', '#dc3545'],
                borderWidth: 1
            }]
        };

        // رسم الحضور
        new Chart(document.getElementById('attendanceChart'), {
            type: 'doughnut',
            data: attendanceData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' }
                }
            }
        });

        // رسم المشاريع
        new Chart(document.getElementById('projectChart'), {
            type: 'bar',
            data: projectData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // رسم التدريب
        new Chart(document.getElementById('trainingChart'), {
            type: 'pie',
            data: trainingData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' }
                }
            }
        });

        // رسم الأداء
        new Chart(document.getElementById('performanceChart'), {
            type: 'line',
            data: {
                labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                datasets: [{
                    label: 'عدد التقييمات',
                    data: [15, 25, 30, 20, 5], // بيانات تجريبية
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        function exportToExcel() {
            window.location.href = '/export_analytics_excel';
        }

        function exportToPDF() {
            window.location.href = '/export_analytics_pdf';
        }

        function refreshData() {
            location.reload();
        }

        // تحديث البيانات كل 5 دقائق
        setInterval(function() {
            fetch('/api/analytics_data')
                .then(response => response.json())
                .then(data => {
                    // تحديث المؤشرات
                    console.log('تم تحديث البيانات:', data);
                });
        }, 300000); // 5 دقائق
    </script>
</body>
</html>
