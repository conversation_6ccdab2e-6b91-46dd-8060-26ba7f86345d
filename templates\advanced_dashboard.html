<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتقدمة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .trend-up {
            color: #28a745;
        }
        .trend-down {
            color: #dc3545;
        }
        .real-time-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        .real-time-indicator.pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم المتقدمة</h2>
                <div class="real-time-indicator pulse">
                    <i class="fas fa-circle me-1"></i>مباشر
                </div>
            </div>
        </div>

        <!-- المقاييس الرئيسية -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card metric-card">
                    <div class="metric-value text-primary" id="totalEmployees">{{ stats.total_employees }}</div>
                    <div class="metric-label">إجمالي الموظفين</div>
                    <small class="trend-up"><i class="fas fa-arrow-up"></i> +5% هذا الشهر</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card metric-card">
                    <div class="metric-value text-success" id="presentToday">{{ stats.present_today }}</div>
                    <div class="metric-label">حاضر اليوم</div>
                    <small class="trend-up"><i class="fas fa-arrow-up"></i> +2% من أمس</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card metric-card">
                    <div class="metric-value text-warning" id="pendingLeaves">{{ stats.pending_leaves }}</div>
                    <div class="metric-label">إجازات معلقة</div>
                    <small class="trend-down"><i class="fas fa-arrow-down"></i> -10% هذا الأسبوع</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card metric-card">
                    <div class="metric-value text-info" id="avgPerformance">{{ stats.avg_performance }}</div>
                    <div class="metric-label">متوسط التقييم</div>
                    <small class="trend-up"><i class="fas fa-arrow-up"></i> +0.2 نقطة</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card metric-card">
                    <div class="metric-value text-danger" id="activeGoals">{{ stats.active_goals }}</div>
                    <div class="metric-label">أهداف نشطة</div>
                    <small class="trend-up"><i class="fas fa-arrow-up"></i> +15% هذا الشهر</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card metric-card">
                    <div class="metric-value text-dark" id="unreadNotifications">{{ stats.unread_notifications }}</div>
                    <div class="metric-label">إشعارات جديدة</div>
                    <small class="text-muted">آخر تحديث: الآن</small>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>اتجاه الحضور الشهري</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="attendanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-star me-2"></i>توزيع تقييمات الأداء</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-building me-2"></i>الموظفين حسب القسم</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bullseye me-2"></i>تقدم الأهداف</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="goalsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plane me-2"></i>أنواع الإجازات</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="leavesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الأنشطة الأخيرة -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>الأنشطة الأخيرة</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>النشاط</th>
                                        <th>المستخدم</th>
                                        <th>التاريخ</th>
                                        <th>التفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody id="recentActivities">
                                    <!-- سيتم تحميل البيانات بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // تحديث البيانات في الوقت الفعلي
        function updateRealTimeData() {
            fetch('/dashboard_data')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalEmployees').textContent = data.total_employees;
                    document.getElementById('presentToday').textContent = data.present_today;
                    document.getElementById('pendingLeaves').textContent = data.pending_leaves;
                    document.getElementById('avgPerformance').textContent = data.avg_rating;
                    document.getElementById('activeGoals').textContent = data.active_goals;
                    document.getElementById('unreadNotifications').textContent = data.unread_notifications;
                })
                .catch(error => {
                    console.error('خطأ في تحديث البيانات:', error);
                });
        }

        // فحص الإشعارات التلقائية
        function checkAutoNotifications() {
            fetch('/auto_notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.notifications_created > 0) {
                        console.log(`تم إنشاء ${data.notifications_created} إشعار جديد`);
                        updateRealTimeData(); // تحديث العدادات
                    }
                })
                .catch(error => {
                    console.error('خطأ في فحص الإشعارات:', error);
                });
        }

        // رسم بياني للحضور الشهري
        const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        const attendanceChart = new Chart(attendanceCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'نسبة الحضور',
                    data: [85, 88, 92, 89, 94, 91],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // رسم بياني لتوزيع تقييمات الأداء
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        const performanceChart = new Chart(performanceCtx, {
            type: 'doughnut',
            data: {
                labels: ['ممتاز (5)', 'جيد جداً (4)', 'جيد (3)', 'مقبول (2)', 'ضعيف (1)'],
                datasets: [{
                    data: [25, 35, 25, 10, 5],
                    backgroundColor: [
                        '#28a745',
                        '#17a2b8',
                        '#ffc107',
                        '#fd7e14',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني للموظفين حسب القسم
        const departmentCtx = document.getElementById('departmentChart').getContext('2d');
        const departmentChart = new Chart(departmentCtx, {
            type: 'bar',
            data: {
                labels: ['الموارد البشرية', 'تقنية المعلومات', 'المالية', 'التسويق'],
                datasets: [{
                    label: 'عدد الموظفين',
                    data: [15, 25, 12, 18],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // رسم بياني لتقدم الأهداف
        const goalsCtx = document.getElementById('goalsChart').getContext('2d');
        const goalsChart = new Chart(goalsCtx, {
            type: 'polarArea',
            data: {
                labels: ['مكتملة', 'قيد التنفيذ', 'متأخرة', 'ملغية'],
                datasets: [{
                    data: [45, 30, 15, 10],
                    backgroundColor: [
                        '#28a745',
                        '#17a2b8',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني لأنواع الإجازات
        const leavesCtx = document.getElementById('leavesChart').getContext('2d');
        const leavesChart = new Chart(leavesCtx, {
            type: 'pie',
            data: {
                labels: ['سنوية', 'مرضية', 'عارضة', 'أمومة'],
                datasets: [{
                    data: [60, 20, 15, 5],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // تحديث البيانات كل 30 ثانية
        setInterval(updateRealTimeData, 30000);

        // فحص الإشعارات كل 5 دقائق
        setInterval(checkAutoNotifications, 300000);

        // تحديث أولي
        updateRealTimeData();

        // فحص أولي للإشعارات
        checkAutoNotifications();

        // تحديث الأنشطة الأخيرة
        function updateRecentActivities() {
            const activities = [
                { action: 'إضافة موظف جديد', user: 'أحمد محمد', time: 'منذ 5 دقائق', details: 'محمد علي - قسم تقنية المعلومات' },
                { action: 'تحديث تقييم أداء', user: 'فاطمة أحمد', time: 'منذ 15 دقيقة', details: 'تقييم سارة محمد - 4.5/5' },
                { action: 'إنشاء نسخة احتياطية', user: 'النظام', time: 'منذ ساعة', details: 'نسخة احتياطية تلقائية' },
                { action: 'إضافة هدف جديد', user: 'علي حسن', time: 'منذ ساعتين', details: 'زيادة الإنتاجية بنسبة 15%' }
            ];

            const tbody = document.getElementById('recentActivities');
            tbody.innerHTML = '';
            
            activities.forEach(activity => {
                const row = `
                    <tr>
                        <td><i class="fas fa-circle text-primary me-2"></i>${activity.action}</td>
                        <td>${activity.user}</td>
                        <td><small class="text-muted">${activity.time}</small></td>
                        <td>${activity.details}</td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // تحديث الأنشطة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateRecentActivities);
    </script>
</body>
</html>
