<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #50e3c2;
            --dark-bg: #2c3e50;
            --light-bg: #f4f7f6;
            --text-color: #333;
            --card-shadow: rgba(0, 0, 0, 0.08);
            --card-hover-shadow: rgba(0, 0, 0, 0.15);
        }

        body {
            background-color: var(--light-bg);
            display: flex;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
        }
        .sidebar {
            width: 280px; /* Increased width for sidebar */
            background-color: var(--dark-bg);
            color: white;
            padding: 25px; /* Increased padding */
            flex-shrink: 0;
            box-shadow: 2px 0 10px var(--card-shadow);
            transition: width 0.3s ease;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 15px 20px; /* Increased padding for nav links */
            display: block;
            text-decoration: none;
            border-radius: 8px;
            transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
            margin-bottom: 10px; /* Increased space between nav items */
            font-size: 1.1rem; /* Slightly larger font size */
        }
        .sidebar .nav-link:hover, 
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
            transform: translateX(5px);
        }
        .sidebar .nav-link i {
            margin-left: 12px; /* Adjusted margin */
            font-size: 1.5rem; /* Slightly larger icon size */
        }
        .content {
            flex-grow: 1;
            padding: 40px; /* Increased padding for content area */
            overflow-y: auto;
        }
        .navbar {
            display: none;
        }
        .card {
            border: none;
            border-radius: 15px; /* More rounded cards */
            box-shadow: 0 8px 25px var(--card-shadow); /* Enhanced shadow */
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        .card:hover {
            transform: translateY(-10px); /* More pronounced lift effect */
            box-shadow: 0 15px 30px var(--card-hover-shadow);
        }
        .card-title {
            color: var(--primary-color);
            font-weight: 700; /* Bolder title */
            font-size: 1.6rem; /* Larger title */
            margin-bottom: 20px; /* More space below title */
        }
        .card-text {
            color: #666;
            font-size: 1rem; /* Slightly larger text */
            line-height: 1.6; /* Improved line spacing */
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 12px 25px; /* Increased padding for buttons */
            border-radius: 8px; /* More rounded buttons */
            transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
            font-size: 1rem; /* Consistent font size */
        }
        .btn-primary:hover {
            background-color: #3a7bd5;
            border-color: #3a7bd5;
            transform: translateY(-3px);
        }
        .btn-secondary {
            background-color: #95a5a6;
            border-color: #95a5a6;
            padding: 12px 25px;
            border-radius: 8px;
            transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
            font-size: 1rem;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
            border-color: #7f8c8d;
            transform: translateY(-3px);
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            border-radius: 10px;
            padding: 20px 25px; /* Increased padding */
            font-size: 1.2rem; /* Larger font size */
            margin-bottom: 40px;
        }
        .icon-large {
            font-size: 3rem; /* تقليل حجم الأيقونات */
            margin-bottom: 15px; /* تقليل المسافة أسفل الأيقونات */
            color: var(--primary-color);
        }
        .card-title {
            color: var(--primary-color);
            font-weight: 600; /* تقليل سمك الخط */
            font-size: 1.3rem; /* تقليل حجم العنوان */
            margin-bottom: 15px; /* تقليل المسافة أسفل العنوان */
        }
        .card-grid .card {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 15px; /* تقليل التباعد الداخلي للبطاقات */
        }
        .card-body {
            padding: 15px; /* تقليل التباعد الداخلي */
        }
        .card-body .btn {
            margin-top: 15px; /* تقليل المسافة فوق الأزرار */
            padding: 8px 20px; /* تقليل حجم الأزرار */
            font-size: 0.9rem; /* تقليل حجم خط الأزرار */
        }
        .sidebar h4 {
            font-size: 1.8rem; /* Larger sidebar title */
            margin-bottom: 35px;
            color: var(--secondary-color);
            font-weight: 700;
        }
        .card-body {
            padding: 20px; /* Consistent padding */
        }
        .card-body .btn {
            margin-top: 20px; /* More space above buttons */
            width: calc(100% - 10px);
            margin-left: 5px;
            margin-right: 5px;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                box-shadow: 0 2px 10px var(--card-shadow);
            }
            body {
                flex-direction: column;
            }
            .content {
                padding: 20px;
            }
            .sidebar .nav-link {
                text-align: center;
            }
            .sidebar .nav-link i {
                margin-left: 0;
                margin-bottom: 5px;
                display: block;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
            <h4 class="text-center mb-4">نظام شؤون الموظفين</h4>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" aria-current="page" href="/dashboard"><i class="fas fa-home"></i> الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/view_employees"><i class="fas fa-users"></i> الموظفون</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> الأقسام</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_attendance') }}"><i class="fas fa-clock"></i> الحضور والانصراف</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_leaves') }}"><i class="fas fa-plane"></i> الإجازات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_performance_reviews') }}"><i class="fas fa-star"></i> التقييم الوظيفي</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_goals') }}"><i class="fas fa-bullseye"></i> الأهداف</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_kpis') }}"><i class="fas fa-tachometer-alt"></i> مؤشرات الأداء</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('view_notifications') }}"><i class="fas fa-bell"></i> الإشعارات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('attendance_report_route') }}"><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_archives') }}"><i class="fas fa-archive"></i> الأرشيف والنسخ الاحتياطية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('manage_roles') }}"><i class="fas fa-user-shield"></i> الأدوار والصلاحيات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('activity_logs') }}"><i class="fas fa-history"></i> سجل العمليات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('advanced_dashboard') }}"><i class="fas fa-tachometer-alt"></i> لوحة التحكم المتقدمة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/logout"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                </li>
            </ul>
    </div>

    <div class="content">
        <div class="alert alert-success" role="alert">
            مرحباً بك في لوحة التحكم الرئيسية لنظام شؤون الموظفين!
        </div>

        <div class="row card-grid">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users icon-large"></i>
                        <h5 class="card-title">الموظفون</h5>
                        <p class="card-text">إدارة بيانات الموظفين.</p>
                        <a href="/view_employees" class="btn btn-primary">عرض الموظفين</a>
                        <a href="/add_employee" class="btn btn-secondary">إضافة موظف</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-building icon-large"></i>
                        <h5 class="card-title">الأقسام</h5>
                        <p class="card-text">إدارة الأقسام والهياكل التنظيمية.</p>
                        <a href="{{ url_for('manage_departments') }}" class="btn btn-primary">عرض الأقسام</a>
                        <a href="{{ url_for('add_department') }}" class="btn btn-secondary">إضافة قسم</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock icon-large"></i>
                        <h5 class="card-title">الحضور والانصراف</h5>
                        <p class="card-text">تسجيل وتتبع حضور وانصراف الموظفين.</p>
                        <a href="{{ url_for('manage_attendance') }}" class="btn btn-primary">عرض الحضور</a>
                        <a href="{{ url_for('manage_departure') }}" class="btn btn-secondary">عرض الانصراف</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-plane icon-large"></i>
                        <h5 class="card-title">الإجازات</h5>
                        <p class="card-text">إدارة طلبات الإجازات والموافقات.</p>
                        <a href="{{ url_for('manage_leaves') }}" class="btn btn-primary">عرض الإجازات</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-alt icon-large"></i>
                        <h5 class="card-title">الوثائق</h5>
                        <p class="card-text">إدارة وثائق الموظفين.</p>
                        <a href="{{ url_for('manage_documents') }}" class="btn btn-primary">عرض الوثائق</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-star icon-large"></i>
                        <h5 class="card-title">التقييم الوظيفي</h5>
                        <p class="card-text">إدارة تقييمات الأداء والتطوير المهني.</p>
                        <a href="{{ url_for('manage_performance_reviews') }}" class="btn btn-primary">عرض التقييمات</a>
                        <a href="{{ url_for('add_performance_review') }}" class="btn btn-secondary">إضافة تقييم</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-bullseye icon-large"></i>
                        <h5 class="card-title">الأهداف</h5>
                        <p class="card-text">تحديد ومتابعة أهداف الموظفين.</p>
                        <a href="{{ url_for('manage_goals') }}" class="btn btn-primary">عرض الأهداف</a>
                        <a href="{{ url_for('add_goal') }}" class="btn btn-secondary">إضافة هدف</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-tachometer-alt icon-large"></i>
                        <h5 class="card-title">مؤشرات الأداء</h5>
                        <p class="card-text">متابعة مؤشرات الأداء الرئيسية.</p>
                        <a href="{{ url_for('manage_kpis') }}" class="btn btn-primary">عرض المؤشرات</a>
                        <a href="{{ url_for('add_kpi') }}" class="btn btn-secondary">إضافة مؤشر</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-bell icon-large"></i>
                        <h5 class="card-title">الإشعارات</h5>
                        <p class="card-text">متابعة الإشعارات والتذكيرات المهمة.</p>
                        <a href="{{ url_for('view_notifications') }}" class="btn btn-primary">عرض الإشعارات</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-archive icon-large"></i>
                        <h5 class="card-title">الأرشيف والنسخ الاحتياطية</h5>
                        <p class="card-text">إدارة الأرشيف والنسخ الاحتياطية.</p>
                        <a href="{{ url_for('manage_archives') }}" class="btn btn-primary">عرض الأرشيف</a>
                        <a href="{{ url_for('manage_backups') }}" class="btn btn-secondary">النسخ الاحتياطية</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-bar icon-large"></i>
                        <h5 class="card-title">التقارير والإحصائيات</h5>
                        <p class="card-text">عرض الرسوم البيانية والتقارير.</p>
                        <a href="{{ url_for('attendance_report_route') }}" class="btn btn-primary">عرض التقارير</a>
                        <canvas id="myChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-tachometer-alt icon-large"></i>
                        <h5 class="card-title">لوحة التحكم المتقدمة</h5>
                        <p class="card-text">إحصائيات متقدمة ورسوم بيانية تفاعلية.</p>
                        <a href="{{ url_for('advanced_dashboard') }}" class="btn btn-primary">عرض اللوحة المتقدمة</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-user-shield icon-large"></i>
                        <h5 class="card-title">الأدوار والصلاحيات</h5>
                        <p class="card-text">إدارة أدوار المستخدمين وصلاحياتهم.</p>
                        <a href="{{ url_for('manage_roles') }}" class="btn btn-primary">إدارة الأدوار</a>
                        <a href="{{ url_for('activity_logs') }}" class="btn btn-secondary">سجل العمليات</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var ctx = document.getElementById('myChart').getContext('2d');
            var myChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['الموظفون', 'الأقسام', 'الإجازات'],
                    datasets: [{
                        label: '# عدد',
                        data: [12, 19, 3],
                        backgroundColor: [
                            'rgba(74, 144, 226, 0.7)', /* Using primary-color */
                            'rgba(80, 227, 194, 0.7)', /* Using secondary-color */
                            'rgba(255, 206, 86, 0.7)'
                        ],
                        borderColor: [
                            'rgba(74, 144, 226, 1)',
                            'rgba(80, 227, 194, 1)',
                            'rgba(255, 206, 86, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false, /* Allow chart to resize freely */
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: false /* Hide y-axis grid lines */
                            }
                        },
                        x: {
                            grid: {
                                display: false /* Hide x-axis grid lines */
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false /* Hide legend */
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>