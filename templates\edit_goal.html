<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الهدف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .section-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .progress {
            height: 25px;
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/manage_goals">
                    <i class="fas fa-arrow-right me-1"></i>العودة للأهداف
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-edit me-2"></i>تعديل الهدف</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- معلومات أساسية -->
                            <div class="section-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات الهدف</h5>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">الموظف <span class="text-danger">*</span></label>
                                    <select class="form-select" name="employee_id" required>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if goal.employee_id == employee.id %}selected{% endif %}>
                                            {{ employee.full_name }} - {{ employee.employee_id }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">عنوان الهدف <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="title" value="{{ goal.title }}" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">وصف الهدف</label>
                                    <textarea class="form-control" name="description" rows="3">{{ goal.description or '' }}</textarea>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label class="form-label">التاريخ المستهدف <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" name="target_date" 
                                           value="{{ goal.target_date.strftime('%Y-%m-%d') }}" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">الأولوية</label>
                                    <select class="form-select" name="priority">
                                        <option value="High" {% if goal.priority == 'High' %}selected{% endif %}>عالية</option>
                                        <option value="Medium" {% if goal.priority == 'Medium' %}selected{% endif %}>متوسطة</option>
                                        <option value="Low" {% if goal.priority == 'Low' %}selected{% endif %}>منخفضة</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-select" name="status">
                                        <option value="Active" {% if goal.status == 'Active' %}selected{% endif %}>نشط</option>
                                        <option value="Completed" {% if goal.status == 'Completed' %}selected{% endif %}>مكتمل</option>
                                        <option value="Cancelled" {% if goal.status == 'Cancelled' %}selected{% endif %}>ملغي</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">نسبة التقدم (%)</label>
                                    <input type="number" class="form-control" name="progress_percentage" 
                                           min="0" max="100" value="{{ goal.progress_percentage }}" id="progressInput">
                                </div>
                            </div>

                            <!-- عرض التقدم -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">التقدم المحرز</label>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" id="progressBar"
                                             style="width: {{ goal.progress_percentage }}%">
                                            {{ goal.progress_percentage }}%
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="4">{{ goal.notes or '' }}</textarea>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            {% if goal.created_date %}
                            <div class="section-header">
                                <h5><i class="fas fa-clock me-2"></i>معلومات إضافية</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">تاريخ الإنشاء: {{ goal.created_date.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </div>
                                    {% if goal.completed_date %}
                                    <div class="col-md-6">
                                        <small class="text-muted">تاريخ الإكمال: {{ goal.completed_date.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('manage_goals') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-1"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث شريط التقدم عند تغيير النسبة
        document.getElementById('progressInput').addEventListener('input', function() {
            const value = this.value;
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = value + '%';
            progressBar.textContent = value + '%';
            
            // تغيير لون الشريط حسب النسبة
            if (value < 30) {
                progressBar.className = 'progress-bar bg-danger';
            } else if (value < 70) {
                progressBar.className = 'progress-bar bg-warning';
            } else {
                progressBar.className = 'progress-bar bg-success';
            }
        });

        // تحديث الحالة تلقائياً عند الوصول لـ 100%
        document.getElementById('progressInput').addEventListener('change', function() {
            const statusSelect = document.querySelector('select[name="status"]');
            if (this.value == 100) {
                statusSelect.value = 'Completed';
            } else if (statusSelect.value == 'Completed' && this.value < 100) {
                statusSelect.value = 'Active';
            }
        });
    </script>
</body>
</html>
