<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأرشيف والنسخ الاحتياطية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .archive-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-archive me-2"></i>إدارة الأرشيف والنسخ الاحتياطية</h2>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card archive-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-archive fa-2x mb-2"></i>
                        <h4>{{ archives|length }}</h4>
                        <p class="mb-0">سجلات مؤرشفة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card archive-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-hdd fa-2x mb-2"></i>
                        <h4>0</h4>
                        <p class="mb-0">نسخ احتياطية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card archive-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-2x mb-2"></i>
                        <h4>0 MB</h4>
                        <p class="mb-0">حجم البيانات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات الأرشفة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-tools me-2"></i>أدوات الأرشفة</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <form method="POST" action="{{ url_for('create_backup') }}">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-save me-2"></i>إنشاء نسخة احتياطية
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('manage_backups') }}" class="btn btn-info w-100">
                            <i class="fas fa-list me-2"></i>عرض النسخ الاحتياطية
                        </a>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-warning w-100" onclick="scheduleBackup()">
                            <i class="fas fa-clock me-2"></i>جدولة نسخ تلقائية
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والتصفية -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="البحث في سبب الأرشفة...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">نوع الجدول</label>
                        <select class="form-select" name="table">
                            <option value="">جميع الجداول</option>
                            <option value="Employee" {% if filter_table == 'Employee' %}selected{% endif %}>الموظفين</option>
                            <option value="Department" {% if filter_table == 'Department' %}selected{% endif %}>الأقسام</option>
                            <option value="Attendance" {% if filter_table == 'Attendance' %}selected{% endif %}>الحضور</option>
                            <option value="Leave" {% if filter_table == 'Leave' %}selected{% endif %}>الإجازات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول الأرشيف -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>السجلات المؤرشفة</h5>
            </div>
            <div class="card-body">
                {% if archives %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع الجدول</th>
                                <th>المعرف الأصلي</th>
                                <th>سبب الأرشفة</th>
                                <th>تاريخ الأرشفة</th>
                                <th>المؤرشف بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for archive in archives %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ archive.table_name }}</span>
                                </td>
                                <td>{{ archive.original_id }}</td>
                                <td>{{ archive.reason or 'غير محدد' }}</td>
                                <td>{{ archive.archived_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ archive.archived_by_user.username }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewArchiveData({{ archive.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="restoreArchive({{ archive.id }})">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteArchive({{ archive.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد سجلات مؤرشفة</h5>
                    <p class="text-muted">ستظهر السجلات المؤرشفة هنا</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal عرض بيانات الأرشيف -->
    <div class="modal fade" id="viewDataModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">بيانات السجل المؤرشف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="archiveData" class="bg-light p-3 rounded"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الاستعادة -->
    <div class="modal fade" id="restoreModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الاستعادة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من استعادة هذا السجل؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="restoreForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-success">استعادة</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewArchiveData(archiveId) {
            // هنا يمكن إضافة AJAX لجلب بيانات الأرشيف
            document.getElementById('archiveData').textContent = 'جاري تحميل البيانات...';
            new bootstrap.Modal(document.getElementById('viewDataModal')).show();
        }

        function restoreArchive(archiveId) {
            document.getElementById('restoreForm').action = '/restore_archive/' + archiveId;
            new bootstrap.Modal(document.getElementById('restoreModal')).show();
        }

        function deleteArchive(archiveId) {
            if (confirm('هل أنت متأكد من حذف هذا السجل المؤرشف نهائياً؟')) {
                // إضافة منطق الحذف
                window.location.href = '/delete_archive/' + archiveId;
            }
        }

        function scheduleBackup() {
            alert('سيتم إضافة ميزة جدولة النسخ الاحتياطية التلقائية قريباً');
        }
    </script>
</body>
</html>
