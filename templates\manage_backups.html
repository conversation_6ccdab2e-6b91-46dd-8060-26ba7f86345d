<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النسخ الاحتياطية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .backup-stats {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/manage_archives">
                    <i class="fas fa-arrow-right me-1"></i>العودة للأرشيف
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-hdd me-2"></i>إدارة النسخ الاحتياطية</h2>
            </div>
        </div>

        <!-- إحصائيات النسخ الاحتياطية -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card backup-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-hdd fa-2x mb-2"></i>
                        <h4>{{ backups|length }}</h4>
                        <p class="mb-0">إجمالي النسخ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card backup-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-2x mb-2"></i>
                        <h4>{{ backups|selectattr('backup_type', 'equalto', 'automatic')|list|length }}</h4>
                        <p class="mb-0">تلقائية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card backup-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-hand-paper fa-2x mb-2"></i>
                        <h4>{{ backups|selectattr('backup_type', 'equalto', 'manual')|list|length }}</h4>
                        <p class="mb-0">يدوية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card backup-stats">
                    <div class="card-body text-center">
                        <i class="fas fa-weight fa-2x mb-2"></i>
                        <h4>{{ "%.1f"|format((backups|sum(attribute='backup_size') or 0) / 1024 / 1024) }} MB</h4>
                        <p class="mb-0">الحجم الإجمالي</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات النسخ الاحتياطية -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <form method="POST" action="{{ url_for('create_backup') }}">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-plus me-2"></i>إنشاء نسخة جديدة
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-warning w-100" onclick="cleanOldBackups()">
                            <i class="fas fa-broom me-2"></i>تنظيف النسخ القديمة
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-info w-100" onclick="exportBackupList()">
                            <i class="fas fa-download me-2"></i>تصدير قائمة النسخ
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول النسخ الاحتياطية -->
        <div class="card">
            <div class="card-body">
                {% if backups %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم النسخة</th>
                                <th>النوع</th>
                                <th>الحجم</th>
                                <th>تاريخ الإنشاء</th>
                                <th>المنشئ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backups %}
                            <tr>
                                <td>
                                    <strong>{{ backup.backup_name }}</strong>
                                    <br><small class="text-muted">{{ backup.file_path }}</small>
                                </td>
                                <td>
                                    {% if backup.backup_type == 'automatic' %}
                                        <span class="badge bg-info">تلقائية</span>
                                    {% else %}
                                        <span class="badge bg-primary">يدوية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if backup.backup_size %}
                                        {{ "%.2f"|format(backup.backup_size / 1024 / 1024) }} MB
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>{{ backup.created_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ backup.created_by_user.username }}</td>
                                <td>
                                    {% if backup.status == 'Completed' %}
                                        <span class="badge bg-success">مكتملة</span>
                                    {% elif backup.status == 'Failed' %}
                                        <span class="badge bg-danger">فاشلة</span>
                                    {% else %}
                                        <span class="badge bg-warning">قيد التنفيذ</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="downloadBackup('{{ backup.file_path }}')">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="restoreBackup({{ backup.id }})">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteBackup({{ backup.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-hdd fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                    <p class="text-muted">قم بإنشاء نسخة احتياطية أولى</p>
                    <form method="POST" action="{{ url_for('create_backup') }}" class="d-inline">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadBackup(filePath) {
            window.location.href = '/download_backup?file=' + encodeURIComponent(filePath);
        }

        function restoreBackup(backupId) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                window.location.href = '/restore_backup/' + backupId;
            }
        }

        function deleteBackup(backupId) {
            if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية نهائياً؟')) {
                window.location.href = '/delete_backup/' + backupId;
            }
        }

        function cleanOldBackups() {
            if (confirm('هل تريد حذف النسخ الاحتياطية الأقدم من 30 يوماً؟')) {
                window.location.href = '/clean_old_backups';
            }
        }

        function exportBackupList() {
            window.location.href = '/export_backup_list';
        }
    </script>
</body>
</html>
