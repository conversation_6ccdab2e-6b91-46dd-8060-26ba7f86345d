<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأهداف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.8rem;
            padding: 8px 12px;
        }
        .progress {
            height: 20px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-bullseye me-2"></i>إدارة الأهداف</h2>
            </div>
        </div>

        <!-- أدوات البحث والتصفية -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="البحث بالاسم أو العنوان...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الموظف</label>
                        <select class="form-select" name="employee_id">
                            <option value="">جميع الموظفين</option>
                            {% for employee in all_employees %}
                            <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>
                                {{ employee.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="Active" {% if filter_status == 'Active' %}selected{% endif %}>نشط</option>
                            <option value="Completed" {% if filter_status == 'Completed' %}selected{% endif %}>مكتمل</option>
                            <option value="Cancelled" {% if filter_status == 'Cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الأولوية</label>
                        <select class="form-select" name="priority">
                            <option value="">جميع الأولويات</option>
                            <option value="High" {% if filter_priority == 'High' %}selected{% endif %}>عالية</option>
                            <option value="Medium" {% if filter_priority == 'Medium' %}selected{% endif %}>متوسطة</option>
                            <option value="Low" {% if filter_priority == 'Low' %}selected{% endif %}>منخفضة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('add_goal') }}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>إضافة هدف
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول الأهداف -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>الموظف</th>
                                <th>التاريخ المستهدف</th>
                                <th>الأولوية</th>
                                <th>التقدم</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for goal in goals %}
                            <tr>
                                <td>
                                    <strong>{{ goal.title }}</strong>
                                    {% if goal.description %}
                                    <br><small class="text-muted">{{ goal.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>{{ goal.employee.full_name }}</td>
                                <td>
                                    {{ goal.target_date.strftime('%Y-%m-%d') }}
                                    {% set days_left = (goal.target_date - today).days %}
                                    {% if days_left < 0 %}
                                        <br><small class="text-danger">متأخر بـ {{ -days_left }} يوم</small>
                                    {% elif days_left == 0 %}
                                        <br><small class="text-warning">اليوم</small>
                                    {% else %}
                                        <br><small class="text-muted">{{ days_left }} يوم متبقي</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if goal.priority == 'High' %}
                                        <span class="badge bg-danger">عالية</span>
                                    {% elif goal.priority == 'Medium' %}
                                        <span class="badge bg-warning">متوسطة</span>
                                    {% else %}
                                        <span class="badge bg-info">منخفضة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ goal.progress_percentage }}%">
                                            {{ goal.progress_percentage }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if goal.status == 'Active' %}
                                        <span class="badge bg-primary">نشط</span>
                                    {% elif goal.status == 'Completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% else %}
                                        <span class="badge bg-secondary">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_goal', goal_id=goal.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ goal.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف هذا الهدف؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(goalId) {
            document.getElementById('deleteForm').action = '/delete_goal/' + goalId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
        
        // تحديد التاريخ الحالي للمقارنة
        const today = new Date();
    </script>
</body>
</html>
