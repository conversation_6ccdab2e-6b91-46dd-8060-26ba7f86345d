<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة مؤشرات الأداء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .progress {
            height: 20px;
            border-radius: 10px;
        }
        .kpi-value {
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-tachometer-alt me-2"></i>إدارة مؤشرات الأداء الرئيسية (KPIs)</h2>
            </div>
        </div>

        <!-- أدوات البحث والتصفية -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="البحث بالاسم أو المؤشر...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الموظف</label>
                        <select class="form-select" name="employee_id">
                            <option value="">جميع الموظفين</option>
                            {% for employee in all_employees %}
                            <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>
                                {{ employee.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">فترة القياس</label>
                        <select class="form-select" name="period">
                            <option value="">جميع الفترات</option>
                            <option value="شهري" {% if filter_period == 'شهري' %}selected{% endif %}>شهري</option>
                            <option value="ربع سنوي" {% if filter_period == 'ربع سنوي' %}selected{% endif %}>ربع سنوي</option>
                            <option value="سنوي" {% if filter_period == 'سنوي' %}selected{% endif %}>سنوي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('add_kpi') }}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>إضافة مؤشر
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول المؤشرات -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم المؤشر</th>
                                <th>الموظف</th>
                                <th>القيمة المستهدفة</th>
                                <th>القيمة الحالية</th>
                                <th>التقدم</th>
                                <th>فترة القياس</th>
                                <th>آخر تحديث</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for kpi in kpis %}
                            <tr>
                                <td>
                                    <strong>{{ kpi.kpi_name }}</strong>
                                    {% if kpi.unit %}
                                    <br><small class="text-muted">الوحدة: {{ kpi.unit }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ kpi.employee.full_name }}</td>
                                <td>
                                    <span class="kpi-value text-primary">{{ kpi.target_value }}</span>
                                    {% if kpi.unit %}<small> {{ kpi.unit }}</small>{% endif %}
                                </td>
                                <td>
                                    <span class="kpi-value">{{ kpi.current_value }}</span>
                                    {% if kpi.unit %}<small> {{ kpi.unit }}</small>{% endif %}
                                </td>
                                <td>
                                    {% set progress = (kpi.current_value / kpi.target_value * 100) if kpi.target_value > 0 else 0 %}
                                    <div class="progress">
                                        {% if progress >= 100 %}
                                            <div class="progress-bar bg-success" style="width: 100%">{{ "%.1f"|format(progress) }}%</div>
                                        {% elif progress >= 75 %}
                                            <div class="progress-bar bg-info" style="width: {{ progress }}%">{{ "%.1f"|format(progress) }}%</div>
                                        {% elif progress >= 50 %}
                                            <div class="progress-bar bg-warning" style="width: {{ progress }}%">{{ "%.1f"|format(progress) }}%</div>
                                        {% else %}
                                            <div class="progress-bar bg-danger" style="width: {{ progress }}%">{{ "%.1f"|format(progress) }}%</div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ kpi.measurement_period }}</span>
                                </td>
                                <td>{{ kpi.last_updated.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="updateKPI({{ kpi.id }}, '{{ kpi.kpi_name }}', {{ kpi.current_value }})">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ kpi.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تحديث المؤشر -->
    <div class="modal fade" id="updateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث مؤشر الأداء</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="updateForm" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">اسم المؤشر</label>
                            <input type="text" class="form-control" id="kpiName" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">القيمة الجديدة</label>
                            <input type="number" class="form-control" name="new_value" id="newValue" step="0.01" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">تحديث</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف هذا المؤشر؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateKPI(kpiId, kpiName, currentValue) {
            document.getElementById('kpiName').value = kpiName;
            document.getElementById('newValue').value = currentValue;
            document.getElementById('updateForm').action = '/update_kpi/' + kpiId;
            new bootstrap.Modal(document.getElementById('updateModal')).show();
        }

        function confirmDelete(kpiId) {
            document.getElementById('deleteForm').action = '/delete_kpi/' + kpiId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
