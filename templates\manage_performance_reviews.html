<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة تقييمات الأداء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.8rem;
            padding: 8px 12px;
        }
        .rating-stars {
            color: #ffc107;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-star me-2"></i>إدارة تقييمات الأداء</h2>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                        <h4>{{ total_reviews }}</h4>
                        <p class="mb-0">إجمالي التقييمات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-edit fa-2x mb-2"></i>
                        <h4>{{ draft_reviews }}</h4>
                        <p class="mb-0">مسودات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-paper-plane fa-2x mb-2"></i>
                        <h4>{{ submitted_reviews }}</h4>
                        <p class="mb-0">مرسلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4>{{ approved_reviews }}</h4>
                        <p class="mb-0">معتمدة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والتصفية -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" placeholder="البحث بالاسم...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الموظف</label>
                        <select class="form-select" name="employee_id">
                            <option value="">جميع الموظفين</option>
                            {% for employee in all_employees %}
                            <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>
                                {{ employee.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="Draft" {% if filter_status == 'Draft' %}selected{% endif %}>مسودة</option>
                            <option value="Submitted" {% if filter_status == 'Submitted' %}selected{% endif %}>مرسلة</option>
                            <option value="Approved" {% if filter_status == 'Approved' %}selected{% endif %}>معتمدة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">السنة</label>
                        <select class="form-select" name="year">
                            <option value="">جميع السنوات</option>
                            {% for year in range(2020, 2030) %}
                            <option value="{{ year }}" {% if filter_year == year %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('add_performance_review') }}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>إضافة تقييم
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول التقييمات -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>المقيم</th>
                                <th>فترة التقييم</th>
                                <th>التقييم العام</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for review in reviews %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong>{{ review.employee.full_name }}</strong><br>
                                            <small class="text-muted">{{ review.employee.employee_id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ review.reviewer.full_name }}</td>
                                <td>
                                    {{ review.review_period_start.strftime('%Y-%m-%d') }}<br>
                                    <small class="text-muted">إلى {{ review.review_period_end.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= review.overall_rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ms-2">{{ review.overall_rating }}/5</span>
                                    </div>
                                </td>
                                <td>
                                    {% if review.status == 'Draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                    {% elif review.status == 'Submitted' %}
                                        <span class="badge bg-warning">مرسلة</span>
                                    {% elif review.status == 'Approved' %}
                                        <span class="badge bg-success">معتمدة</span>
                                    {% endif %}
                                </td>
                                <td>{{ review.created_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_performance_review', review_id=review.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ review.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف هذا التقييم؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(reviewId) {
            document.getElementById('deleteForm').action = '/delete_performance_review/' + reviewId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
