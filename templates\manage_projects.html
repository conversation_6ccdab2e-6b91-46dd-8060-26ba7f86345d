<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشاريع - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .btn-primary { background: linear-gradient(45deg, #007bff, #0056b3); border: none; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status-badge { font-size: 0.8em; padding: 0.3em 0.6em; }
        .priority-high { color: #dc3545; }
        .priority-medium { color: #ffc107; }
        .priority-low { color: #28a745; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('manage_projects') }}"><i class="fas fa-project-diagram"></i> المشاريع</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_tasks') }}"><i class="fas fa-tasks"></i> المهام</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-project-diagram"></i> إدارة المشاريع</h4>
                        <a href="{{ url_for('add_project') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة مشروع جديد
                        </a>
                    </div>
                    <div class="card-body">
                        <!-- فلاتر البحث -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-3">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="البحث في المشاريع..." value="{{ search_query }}">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="Active" {% if filter_status == 'Active' %}selected{% endif %}>نشط</option>
                                    <option value="Completed" {% if filter_status == 'Completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="On Hold" {% if filter_status == 'On Hold' %}selected{% endif %}>معلق</option>
                                    <option value="Cancelled" {% if filter_status == 'Cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" name="department_id">
                                    <option value="">جميع الأقسام</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if filter_department_id == dept.id %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                            <div class="col-md-2">
                                <a href="{{ url_for('manage_projects') }}" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </form>

                        <!-- جدول المشاريع -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المشروع</th>
                                        <th>المدير</th>
                                        <th>القسم</th>
                                        <th>تاريخ البداية</th>
                                        <th>تاريخ النهاية</th>
                                        <th>الحالة</th>
                                        <th>الأولوية</th>
                                        <th>التقدم</th>
                                        <th>الميزانية</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for project in projects %}
                                    <tr>
                                        <td>
                                            <strong>{{ project.name }}</strong>
                                            {% if project.description %}
                                            <br><small class="text-muted">{{ project.description[:50] }}...</small>
                                            {% endif %}
                                        </td>
                                        <td>{{ project.manager.full_name }}</td>
                                        <td>{{ project.department.name if project.department else 'غير محدد' }}</td>
                                        <td>{{ project.start_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                        <td>
                                            {% if project.status == 'Active' %}
                                                <span class="badge bg-success status-badge">نشط</span>
                                            {% elif project.status == 'Completed' %}
                                                <span class="badge bg-primary status-badge">مكتمل</span>
                                            {% elif project.status == 'On Hold' %}
                                                <span class="badge bg-warning status-badge">معلق</span>
                                            {% else %}
                                                <span class="badge bg-danger status-badge">ملغي</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="priority-{{ project.priority.lower() }}">
                                                <i class="fas fa-circle"></i>
                                                {% if project.priority == 'High' %}عالية
                                                {% elif project.priority == 'Medium' %}متوسطة
                                                {% else %}منخفضة{% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ project.progress_percentage }}%">
                                                    {{ project.progress_percentage }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ "{:,.0f}".format(project.budget) if project.budget else 'غير محدد' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('project_details', project_id=project.id) }}" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('edit_project', project_id=project.id) }}" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete({{ project.id }}, '{{ project.name }}')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="10" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-3x mb-3"></i>
                                            <br>لا توجد مشاريع مطابقة للبحث
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف المشروع "<span id="projectName"></span>"؟
                    <br><small class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(projectId, projectName) {
            document.getElementById('projectName').textContent = projectName;
            document.getElementById('deleteForm').action = '/delete_project/' + projectId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
