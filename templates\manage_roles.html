<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأدوار والصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .permission-badge {
            font-size: 0.7rem;
            margin: 2px;
            padding: 4px 8px;
        }
        .role-card {
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-user-shield me-2"></i>إدارة الأدوار والصلاحيات</h2>
                <a href="{{ url_for('add_role') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دور جديد
                </a>
            </div>
        </div>

        <div class="row">
            {% for role in roles %}
            <div class="col-md-6 mb-4">
                <div class="card role-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-tag me-2"></i>{{ role.name }}
                        </h5>
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('edit_role', role_id=role.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="confirmDelete({{ role.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">{{ role.description or 'لا يوجد وصف' }}</p>
                        
                        <h6>الصلاحيات:</h6>
                        <div class="mb-3">
                            {% if role.permissions %}
                                {% set permissions = role.permissions|from_json %}
                                {% for permission in permissions %}
                                    <span class="badge bg-primary permission-badge">
                                        {% if permission == 'view_employees' %}عرض الموظفين
                                        {% elif permission == 'edit_employees' %}تعديل الموظفين
                                        {% elif permission == 'delete_employees' %}حذف الموظفين
                                        {% elif permission == 'manage_departments' %}إدارة الأقسام
                                        {% elif permission == 'manage_attendance' %}إدارة الحضور
                                        {% elif permission == 'manage_leaves' %}إدارة الإجازات
                                        {% elif permission == 'view_reports' %}عرض التقارير
                                        {% elif permission == 'manage_performance' %}إدارة التقييمات
                                        {% elif permission == 'manage_goals' %}إدارة الأهداف
                                        {% elif permission == 'manage_kpis' %}إدارة المؤشرات
                                        {% elif permission == 'manage_archives' %}إدارة الأرشيف
                                        {% elif permission == 'manage_users' %}إدارة المستخدمين
                                        {% else %}{{ permission }}
                                        {% endif %}
                                    </span>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">لا توجد صلاحيات محددة</span>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                {{ role.users|length }} مستخدم
                            </small>
                            <small class="text-muted">
                                تاريخ الإنشاء: {{ role.created_date.strftime('%Y-%m-%d') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not roles %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أدوار محددة</h5>
                <p class="text-muted">قم بإنشاء أدوار لتنظيم صلاحيات المستخدمين</p>
                <a href="{{ url_for('add_role') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دور جديد
                </a>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف هذا الدور؟ لن تتمكن من التراجع عن هذا الإجراء.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(roleId) {
            document.getElementById('deleteForm').action = '/delete_role/' + roleId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
