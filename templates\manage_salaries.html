<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرواتب - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status-badge { font-size: 0.8em; padding: 0.3em 0.6em; }
        .salary-amount { font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link active" href="{{ url_for('manage_salaries') }}"><i class="fas fa-money-bill-wave"></i> الرواتب</a>
                <a class="nav-link" href="{{ url_for('salary_report') }}"><i class="fas fa-chart-line"></i> تقارير الرواتب</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-money-bill-wave"></i> إدارة الرواتب</h4>
                <a href="{{ url_for('add_salary') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة راتب جديد
                </a>
            </div>
            <div class="card-body">
                <!-- فلاتر البحث -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في الموظفين..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="employee_id">
                            <option value="">جميع الموظفين</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>
                                {{ employee.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="Pending" {% if filter_status == 'Pending' %}selected{% endif %}>معلق</option>
                            <option value="Paid" {% if filter_status == 'Paid' %}selected{% endif %}>مدفوع</option>
                            <option value="Cancelled" {% if filter_status == 'Cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="month" class="form-control" name="month" value="{{ filter_month }}">
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="{{ url_for('manage_salaries') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </form>

                <!-- جدول الرواتب -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>فترة الراتب</th>
                                <th>الراتب الأساسي</th>
                                <th>البدلات</th>
                                <th>الإضافي</th>
                                <th>المكافآت</th>
                                <th>الخصومات</th>
                                <th>الصافي</th>
                                <th>الحالة</th>
                                <th>تاريخ الدفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for salary in salaries %}
                            <tr>
                                <td>
                                    <strong>{{ salary.employee.full_name }}</strong>
                                    <br><small class="text-muted">{{ salary.employee.employee_id }}</small>
                                </td>
                                <td>
                                    {{ salary.pay_period_start.strftime('%Y-%m-%d') }}<br>
                                    <small class="text-muted">إلى {{ salary.pay_period_end.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td class="salary-amount">{{ "{:,.0f}".format(salary.basic_salary) }}</td>
                                <td>{{ "{:,.0f}".format(salary.allowances) if salary.allowances else '0' }}</td>
                                <td>
                                    {% if salary.overtime_hours %}
                                    {{ salary.overtime_hours }} س<br>
                                    <small>{{ "{:,.0f}".format(salary.overtime_hours * salary.overtime_rate) }}</small>
                                    {% else %}
                                    0
                                    {% endif %}
                                </td>
                                <td>{{ "{:,.0f}".format(salary.bonus) if salary.bonus else '0' }}</td>
                                <td class="text-danger">{{ "{:,.0f}".format(salary.deductions) if salary.deductions else '0' }}</td>
                                <td class="salary-amount fs-5">{{ "{:,.0f}".format(salary.net_salary) }}</td>
                                <td>
                                    {% if salary.status == 'Pending' %}
                                        <span class="badge bg-warning status-badge">معلق</span>
                                    {% elif salary.status == 'Paid' %}
                                        <span class="badge bg-success status-badge">مدفوع</span>
                                    {% else %}
                                        <span class="badge bg-danger status-badge">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ salary.payment_date.strftime('%Y-%m-%d') if salary.payment_date else 'غير محدد' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if salary.status == 'Pending' %}
                                        <button class="btn btn-sm btn-success" 
                                                onclick="markAsPaid({{ salary.id }})" title="تأكيد الدفع">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                        <a href="{{ url_for('edit_salary', salary_id=salary.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-info" 
                                                onclick="viewDetails({{ salary.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ salary.id }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="11" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <br>لا توجد رواتب مطابقة للبحث
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- إحصائيات سريعة -->
                {% if salaries %}
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5>إجمالي الرواتب</h5>
                                <h3>{{ "{:,.0f}".format(salaries|sum(attribute='net_salary')) }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>المدفوع</h5>
                                <h3>{{ salaries|selectattr('status', 'equalto', 'Paid')|list|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5>المعلق</h5>
                                <h3>{{ salaries|selectattr('status', 'equalto', 'Pending')|list|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5>متوسط الراتب</h5>
                                <h3>{{ "{:,.0f}".format((salaries|sum(attribute='net_salary')) / (salaries|length)) }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل الراتب -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الراتب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="salaryDetails">
                    <!-- سيتم تحميل التفاصيل هنا -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function markAsPaid(salaryId) {
            if (confirm('هل أنت متأكد من تأكيد دفع هذا الراتب؟')) {
                fetch('/mark_salary_paid/' + salaryId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                });
            }
        }

        function viewDetails(salaryId) {
            fetch('/salary_details/' + salaryId)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('salaryDetails').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                });
        }

        function confirmDelete(salaryId) {
            if (confirm('هل أنت متأكد من حذف هذا الراتب؟')) {
                fetch('/delete_salary/' + salaryId, {
                    method: 'POST'
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    }
                });
            }
        }
    </script>
</body>
</html>
