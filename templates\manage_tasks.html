<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهام - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status-badge { font-size: 0.8em; padding: 0.3em 0.6em; }
        .priority-high { color: #dc3545; }
        .priority-medium { color: #ffc107; }
        .priority-low { color: #28a745; }
        .progress-input { width: 80px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ url_for('manage_projects') }}"><i class="fas fa-project-diagram"></i> المشاريع</a>
                <a class="nav-link active" href="{{ url_for('manage_tasks') }}"><i class="fas fa-tasks"></i> المهام</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-tasks"></i> إدارة المهام</h4>
                <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة مهمة جديدة
                </a>
            </div>
            <div class="card-body">
                <!-- فلاتر البحث -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في المهام..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="To Do" {% if filter_status == 'To Do' %}selected{% endif %}>للتنفيذ</option>
                            <option value="In Progress" {% if filter_status == 'In Progress' %}selected{% endif %}>قيد التنفيذ</option>
                            <option value="Review" {% if filter_status == 'Review' %}selected{% endif %}>للمراجعة</option>
                            <option value="Done" {% if filter_status == 'Done' %}selected{% endif %}>مكتملة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="project_id">
                            <option value="">جميع المشاريع</option>
                            {% for project in projects %}
                            <option value="{{ project.id }}" {% if filter_project_id == project.id %}selected{% endif %}>
                                {{ project.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="assigned_to">
                            <option value="">جميع الموظفين</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" {% if filter_assigned_to == employee.id %}selected{% endif %}>
                                {{ employee.full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="{{ url_for('manage_tasks') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </form>

                <!-- جدول المهام -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>عنوان المهمة</th>
                                <th>المشروع</th>
                                <th>المكلف</th>
                                <th>الأولوية</th>
                                <th>الحالة</th>
                                <th>التقدم</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الساعات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr>
                                <td>
                                    <strong>{{ task.title }}</strong>
                                    {% if task.description %}
                                    <br><small class="text-muted">{{ task.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>{{ task.project.name if task.project else 'غير محدد' }}</td>
                                <td>{{ task.assignee.full_name }}</td>
                                <td>
                                    <span class="priority-{{ task.priority.lower() }}">
                                        <i class="fas fa-circle"></i>
                                        {% if task.priority == 'High' %}عالية
                                        {% elif task.priority == 'Medium' %}متوسطة
                                        {% else %}منخفضة{% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if task.status == 'To Do' %}
                                        <span class="badge bg-secondary status-badge">للتنفيذ</span>
                                    {% elif task.status == 'In Progress' %}
                                        <span class="badge bg-warning status-badge">قيد التنفيذ</span>
                                    {% elif task.status == 'Review' %}
                                        <span class="badge bg-info status-badge">للمراجعة</span>
                                    {% else %}
                                        <span class="badge bg-success status-badge">مكتملة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress me-2" style="width: 100px; height: 20px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ task.progress_percentage }}%">
                                                {{ task.progress_percentage }}%
                                            </div>
                                        </div>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="updateProgress({{ task.id }}, {{ task.progress_percentage }}, {{ task.actual_hours or 0 }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    {% if task.due_date %}
                                        {{ task.due_date.strftime('%Y-%m-%d') }}
                                        {% set days_left = (task.due_date - today).days %}
                                        {% if days_left < 0 %}
                                            <br><small class="text-danger">متأخر {{ -days_left }} يوم</small>
                                        {% elif days_left == 0 %}
                                            <br><small class="text-warning">اليوم</small>
                                        {% elif days_left <= 3 %}
                                            <br><small class="text-warning">{{ days_left }} أيام</small>
                                        {% endif %}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>
                                    <small>
                                        المقدر: {{ task.estimated_hours or 'غير محدد' }}<br>
                                        الفعلي: {{ task.actual_hours or 0 }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_task', task_id=task.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ task.id }}, '{{ task.title }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <br>لا توجد مهام مطابقة للبحث
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تحديث التقدم -->
    <div class="modal fade" id="progressModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحديث تقدم المهمة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="progressForm" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="progress" class="form-label">نسبة التقدم (%)</label>
                            <input type="range" class="form-range" id="progress" name="progress" 
                                   min="0" max="100" value="0" oninput="updateProgressValue(this.value)">
                            <div class="text-center mt-2">
                                <span id="progressValue" class="badge bg-primary">0%</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="actual_hours" class="form-label">الساعات الفعلية</label>
                            <input type="number" class="form-control" id="actual_hours" name="actual_hours" 
                                   step="0.5" min="0">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التحديث</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف المهمة "<span id="taskTitle"></span>"؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateProgress(taskId, currentProgress, currentHours) {
            document.getElementById('progress').value = currentProgress;
            document.getElementById('progressValue').textContent = currentProgress + '%';
            document.getElementById('actual_hours').value = currentHours;
            document.getElementById('progressForm').action = '/update_task_progress/' + taskId;
            new bootstrap.Modal(document.getElementById('progressModal')).show();
        }

        function updateProgressValue(value) {
            document.getElementById('progressValue').textContent = value + '%';
        }

        function confirmDelete(taskId, taskTitle) {
            document.getElementById('taskTitle').textContent = taskTitle;
            document.getElementById('deleteForm').action = '/delete_task/' + taskId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // تحديث ألوان شريط التقدم حسب النسبة
        document.addEventListener('DOMContentLoaded', function() {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const percentage = parseInt(bar.textContent);
                if (percentage < 30) {
                    bar.classList.add('bg-danger');
                } else if (percentage < 70) {
                    bar.classList.add('bg-warning');
                } else {
                    bar.classList.add('bg-success');
                }
            });
        });
    </script>
</body>
</html>
