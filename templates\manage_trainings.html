<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التدريب - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .status-badge { font-size: 0.8em; padding: 0.3em 0.6em; }
        .training-card { transition: transform 0.2s; }
        .training-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link active" href="{{ url_for('manage_trainings') }}"><i class="fas fa-graduation-cap"></i> التدريب</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-graduation-cap"></i> إدارة التدريب والتطوير</h4>
                <a href="{{ url_for('add_training') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة تدريب جديد
                </a>
            </div>
            <div class="card-body">
                <!-- فلاتر البحث -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في التدريبات..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="Planned" {% if filter_status == 'Planned' %}selected{% endif %}>مخطط</option>
                            <option value="Ongoing" {% if filter_status == 'Ongoing' %}selected{% endif %}>جاري</option>
                            <option value="Completed" {% if filter_status == 'Completed' %}selected{% endif %}>مكتمل</option>
                            <option value="Cancelled" {% if filter_status == 'Cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="training_type">
                            <option value="">جميع الأنواع</option>
                            <option value="Internal" {% if filter_type == 'Internal' %}selected{% endif %}>داخلي</option>
                            <option value="External" {% if filter_type == 'External' %}selected{% endif %}>خارجي</option>
                            <option value="Online" {% if filter_type == 'Online' %}selected{% endif %}>إلكتروني</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="{{ url_for('manage_trainings') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </form>

                <!-- عرض التدريبات كبطاقات -->
                <div class="row">
                    {% for training in trainings %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card training-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">{{ training.title }}</h6>
                                {% if training.status == 'Planned' %}
                                    <span class="badge bg-info status-badge">مخطط</span>
                                {% elif training.status == 'Ongoing' %}
                                    <span class="badge bg-warning status-badge">جاري</span>
                                {% elif training.status == 'Completed' %}
                                    <span class="badge bg-success status-badge">مكتمل</span>
                                {% else %}
                                    <span class="badge bg-danger status-badge">ملغي</span>
                                {% endif %}
                            </div>
                            <div class="card-body">
                                <p class="card-text">{{ training.description[:100] }}...</p>
                                
                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">المدرب</small>
                                        <div><strong>{{ training.trainer_name or 'غير محدد' }}</strong></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">النوع</small>
                                        <div>
                                            {% if training.training_type == 'Internal' %}
                                                <i class="fas fa-building text-primary"></i> داخلي
                                            {% elif training.training_type == 'External' %}
                                                <i class="fas fa-external-link-alt text-success"></i> خارجي
                                            {% else %}
                                                <i class="fas fa-laptop text-info"></i> إلكتروني
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">تاريخ البداية</small>
                                        <div>{{ training.start_date.strftime('%Y-%m-%d') }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">المدة</small>
                                        <div>{{ training.duration_hours or 'غير محدد' }} ساعة</div>
                                    </div>
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">المشاركين</small>
                                        <div>
                                            <span class="badge bg-primary">
                                                {{ training.enrollments|length }}/{{ training.max_participants or '∞' }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">التكلفة</small>
                                        <div>{{ "{:,.0f}".format(training.cost) if training.cost else 'مجاني' }}</div>
                                    </div>
                                </div>

                                {% if training.location %}
                                <div class="text-center mb-3">
                                    <small class="text-muted">المكان</small>
                                    <div><i class="fas fa-map-marker-alt"></i> {{ training.location }}</div>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <a href="{{ url_for('training_enrollments', training_id=training.id) }}" 
                                       class="btn btn-sm btn-outline-info" title="المسجلين">
                                        <i class="fas fa-users"></i>
                                    </a>
                                    <a href="{{ url_for('edit_training', training_id=training.id) }}" 
                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete({{ training.id }}, '{{ training.title }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="col-12">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-graduation-cap fa-4x mb-3"></i>
                            <h4>لا توجد تدريبات</h4>
                            <p>لم يتم العثور على تدريبات مطابقة للبحث</p>
                            <a href="{{ url_for('add_training') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة تدريب جديد
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- إحصائيات سريعة -->
                {% if trainings %}
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5>إجمالي التدريبات</h5>
                                <h3>{{ trainings|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5>جاري التنفيذ</h5>
                                <h3>{{ trainings|selectattr('status', 'equalto', 'Ongoing')|list|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>مكتملة</h5>
                                <h3>{{ trainings|selectattr('status', 'equalto', 'Completed')|list|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5>إجمالي المشاركين</h5>
                                <h3>{{ trainings|sum(attribute='enrollments')|length }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف التدريب "<span id="trainingTitle"></span>"؟
                    <br><small class="text-danger">سيتم حذف جميع التسجيلات المرتبطة بهذا التدريب.</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(trainingId, trainingTitle) {
            document.getElementById('trainingTitle').textContent = trainingTitle;
            document.getElementById('deleteForm').action = '/delete_training/' + trainingId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // تحديث ألوان البطاقات حسب الحالة
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.training-card');
            cards.forEach(card => {
                const status = card.querySelector('.status-badge').textContent.trim();
                if (status === 'جاري') {
                    card.style.borderLeft = '4px solid #ffc107';
                } else if (status === 'مكتمل') {
                    card.style.borderLeft = '4px solid #28a745';
                } else if (status === 'مخطط') {
                    card.style.borderLeft = '4px solid #17a2b8';
                } else {
                    card.style.borderLeft = '4px solid #dc3545';
                }
            });
        });
    </script>
</body>
</html>
