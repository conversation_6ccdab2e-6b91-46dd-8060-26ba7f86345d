<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .notification-item {
            border-left: 4px solid #667eea;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .notification-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .notification-item.unread {
            background-color: #f8f9ff;
            border-left-color: #ff6b6b;
        }
        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        .notification-icon.leave_expiry {
            background-color: #ffe6e6;
            color: #ff4757;
        }
        .notification-icon.review_reminder {
            background-color: #e6f3ff;
            color: #3742fa;
        }
        .notification-icon.important_date {
            background-color: #fff3e6;
            color: #ff9f43;
        }
        .unread-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            border-radius: 20px;
            padding: 5px 12px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-bell me-2"></i>الإشعارات</h2>
                {% if unread_count > 0 %}
                <span class="unread-badge">{{ unread_count }} غير مقروء</span>
                {% endif %}
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                {% if notifications %}
                    {% for notification in notifications %}
                    <div class="card notification-item {% if not notification.is_read %}unread{% endif %}">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="notification-icon {{ notification.notification_type }}">
                                        {% if notification.notification_type == 'leave_expiry' %}
                                            <i class="fas fa-calendar-times"></i>
                                        {% elif notification.notification_type == 'review_reminder' %}
                                            <i class="fas fa-star"></i>
                                        {% elif notification.notification_type == 'important_date' %}
                                            <i class="fas fa-exclamation-triangle"></i>
                                        {% else %}
                                            <i class="fas fa-info-circle"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 {% if not notification.is_read %}fw-bold{% endif %}">
                                                {{ notification.title }}
                                            </h6>
                                            <p class="mb-1 text-muted">{{ notification.message }}</p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                {{ notification.created_date.strftime('%Y-%m-%d %H:%M') }}
                                            </small>
                                        </div>
                                        <div>
                                            {% if not notification.is_read %}
                                            <a href="{{ url_for('mark_notification_read', notification_id=notification.id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-check me-1"></i>تحديد كمقروء
                                            </a>
                                            {% else %}
                                            <span class="badge bg-success">مقروء</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات</h5>
                            <p class="text-muted">ستظهر الإشعارات هنا عند توفرها</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- إحصائيات الإشعارات -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>إحصائيات الإشعارات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-bell fa-2x text-primary mb-2"></i>
                                    <h4>{{ notifications|length }}</h4>
                                    <small class="text-muted">إجمالي الإشعارات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-envelope fa-2x text-danger mb-2"></i>
                                    <h4>{{ unread_count }}</h4>
                                    <small class="text-muted">غير مقروءة</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-calendar-times fa-2x text-warning mb-2"></i>
                                    <h4>{{ notifications|selectattr('notification_type', 'equalto', 'leave_expiry')|list|length }}</h4>
                                    <small class="text-muted">انتهاء إجازات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="fas fa-star fa-2x text-info mb-2"></i>
                                    <h4>{{ notifications|selectattr('notification_type', 'equalto', 'review_reminder')|list|length }}</h4>
                                    <small class="text-muted">تذكيرات تقييم</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الصفحة كل 5 دقائق للحصول على إشعارات جديدة
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 دقائق
    </script>
</body>
</html>
