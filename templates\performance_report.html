<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأداء الشامل - {{ employee.full_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .employee-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
        }
        .rating-stars {
            color: #ffc107;
            font-size: 1.2rem;
        }
        .progress {
            height: 25px;
            border-radius: 12px;
        }
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        @media print {
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark no-print">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-chart-line me-2"></i>نظام شؤون الموظفين
            </a>
            <div class="navbar-nav ms-auto">
                <button onclick="window.print()" class="btn btn-light btn-sm me-2">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
                <a class="nav-link" href="/manage_performance_reviews">
                    <i class="fas fa-arrow-right me-1"></i>العودة للتقييمات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- معلومات الموظف -->
        <div class="employee-header mb-4">
            <h2><i class="fas fa-user me-2"></i>{{ employee.full_name }}</h2>
            <p class="mb-0">{{ employee.employee_id }} - {{ employee.department.name if employee.department else 'غير محدد' }}</p>
            <p class="mb-0">{{ employee.position or 'غير محدد' }}</p>
        </div>

        <!-- ملخص الأداء -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value text-primary">{{ reviews|length }}</div>
                    <div class="text-muted">تقييمات الأداء</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value text-success">
                        {% if reviews %}
                            {{ "%.1f"|format(reviews|map(attribute='overall_rating')|sum / reviews|length) }}
                        {% else %}
                            0.0
                        {% endif %}
                    </div>
                    <div class="text-muted">متوسط التقييم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value text-info">{{ goals|length }}</div>
                    <div class="text-muted">إجمالي الأهداف</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="metric-value text-warning">{{ kpis|length }}</div>
                    <div class="text-muted">مؤشرات الأداء</div>
                </div>
            </div>
        </div>

        <!-- تقييمات الأداء -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-star me-2"></i>تقييمات الأداء</h5>
            </div>
            <div class="card-body">
                {% if reviews %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>فترة التقييم</th>
                                <th>المقيم</th>
                                <th>التقييم العام</th>
                                <th>المهارات التقنية</th>
                                <th>التواصل</th>
                                <th>العمل الجماعي</th>
                                <th>القيادة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for review in reviews %}
                            <tr>
                                <td>
                                    {{ review.review_period_start.strftime('%Y-%m-%d') }}<br>
                                    <small class="text-muted">إلى {{ review.review_period_end.strftime('%Y-%m-%d') }}</small>
                                </td>
                                <td>{{ review.reviewer.full_name }}</td>
                                <td>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= review.overall_rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ms-2">{{ review.overall_rating }}</span>
                                    </div>
                                </td>
                                <td>{{ review.technical_skills }}</td>
                                <td>{{ review.communication_skills }}</td>
                                <td>{{ review.teamwork }}</td>
                                <td>{{ review.leadership }}</td>
                                <td>
                                    {% if review.status == 'Approved' %}
                                        <span class="badge bg-success">معتمد</span>
                                    {% elif review.status == 'Submitted' %}
                                        <span class="badge bg-warning">مرسل</span>
                                    {% else %}
                                        <span class="badge bg-secondary">مسودة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-star fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد تقييمات أداء لهذا الموظف</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- الأهداف -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bullseye me-2"></i>الأهداف</h5>
            </div>
            <div class="card-body">
                {% if goals %}
                <div class="row">
                    {% for goal in goals %}
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6>{{ goal.title }}</h6>
                                <span class="badge 
                                    {% if goal.status == 'Completed' %}bg-success
                                    {% elif goal.status == 'Active' %}bg-primary
                                    {% else %}bg-secondary{% endif %}">
                                    {{ goal.status }}
                                </span>
                            </div>
                            <p class="text-muted small">{{ goal.description or 'لا يوجد وصف' }}</p>
                            <div class="mb-2">
                                <small class="text-muted">التقدم:</small>
                                <div class="progress">
                                    <div class="progress-bar 
                                        {% if goal.progress_percentage >= 100 %}bg-success
                                        {% elif goal.progress_percentage >= 75 %}bg-info
                                        {% elif goal.progress_percentage >= 50 %}bg-warning
                                        {% else %}bg-danger{% endif %}" 
                                         style="width: {{ goal.progress_percentage }}%">
                                        {{ goal.progress_percentage }}%
                                    </div>
                                </div>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                الموعد النهائي: {{ goal.target_date.strftime('%Y-%m-%d') }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-bullseye fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد أهداف محددة لهذا الموظف</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- مؤشرات الأداء -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt me-2"></i>مؤشرات الأداء الرئيسية</h5>
            </div>
            <div class="card-body">
                {% if kpis %}
                <div class="row">
                    {% for kpi in kpis %}
                    <div class="col-md-6 mb-3">
                        <div class="border rounded p-3">
                            <h6>{{ kpi.kpi_name }}</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">المستهدف:</small>
                                    <div class="fw-bold text-primary">{{ kpi.target_value }} {{ kpi.unit or '' }}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الحالي:</small>
                                    <div class="fw-bold">{{ kpi.current_value }} {{ kpi.unit or '' }}</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                {% set progress = (kpi.current_value / kpi.target_value * 100) if kpi.target_value > 0 else 0 %}
                                <div class="progress">
                                    <div class="progress-bar 
                                        {% if progress >= 100 %}bg-success
                                        {% elif progress >= 75 %}bg-info
                                        {% elif progress >= 50 %}bg-warning
                                        {% else %}bg-danger{% endif %}" 
                                         style="width: {{ progress }}%">
                                        {{ "%.1f"|format(progress) }}%
                                    </div>
                                </div>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                فترة القياس: {{ kpi.measurement_period }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-tachometer-alt fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مؤشرات أداء محددة لهذا الموظف</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- الرسم البياني لتطور الأداء -->
        {% if reviews|length > 1 %}
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>تطور الأداء عبر الزمن</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="performanceTrendChart"></canvas>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        {% if reviews|length > 1 %}
        // رسم بياني لتطور الأداء
        const ctx = document.getElementById('performanceTrendChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [
                    {% for review in reviews|reverse %}
                    '{{ review.review_period_start.strftime("%Y-%m") }}'{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                datasets: [{
                    label: 'التقييم العام',
                    data: [
                        {% for review in reviews|reverse %}
                        {{ review.overall_rating }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'المهارات التقنية',
                    data: [
                        {% for review in reviews|reverse %}
                        {{ review.technical_skills }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }, {
                    label: 'مهارات التواصل',
                    data: [
                        {% for review in reviews|reverse %}
                        {{ review.communication_skills }}{% if not loop.last %},{% endif %}
                        {% endfor %}
                    ],
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        {% endif %}
    </script>
</body>
</html>
