<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الرواتب - نظام إدارة شؤون الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; margin-bottom: 20px; }
        .stats-card { text-align: center; padding: 20px; }
        .stats-value { font-size: 2em; font-weight: bold; }
        .chart-container { position: relative; height: 300px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-building"></i> نظام إدارة شؤون الموظفين
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ url_for('manage_salaries') }}"><i class="fas fa-money-bill-wave"></i> إدارة الرواتب</a>
                <a class="nav-link active" href="{{ url_for('salary_report') }}"><i class="fas fa-chart-line"></i> تقارير الرواتب</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-chart-bar"></i> تقرير الرواتب والإحصائيات</h2>
                <p class="text-muted">تحليل شامل لرواتب الموظفين والمدفوعات</p>
            </div>
        </div>

        <!-- فلاتر التقرير -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> فلاتر التقرير</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="month" class="form-label">الشهر</label>
                        <input type="month" class="form-control" id="month" name="month" value="{{ filter_month }}">
                    </div>
                    <div class="col-md-4">
                        <label for="department_id" class="form-label">القسم</label>
                        <select class="form-select" name="department_id">
                            <option value="">جميع الأقسام</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if filter_department_id == dept.id %}selected{% endif %}>
                                {{ dept.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> تطبيق الفلاتر
                        </button>
                        <a href="{{ url_for('salary_report') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white stats-card">
                    <div class="stats-value">{{ "{:,.0f}".format(stats.total_salaries) }}</div>
                    <div>إجمالي الرواتب (ريال)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white stats-card">
                    <div class="stats-value">{{ "{:,.0f}".format(stats.avg_salary) }}</div>
                    <div>متوسط الراتب (ريال)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white stats-card">
                    <div class="stats-value">{{ stats.paid_count }}</div>
                    <div>الرواتب المدفوعة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white stats-card">
                    <div class="stats-value">{{ stats.pending_count }}</div>
                    <div>الرواتب المعلقة</div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> توزيع الرواتب حسب القسم</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> حالات الرواتب</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول تفصيلي -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-table"></i> تفاصيل الرواتب</h5>
                <div class="btn-group">
                    <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Excel
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="salaryTable">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>الراتب الأساسي</th>
                                <th>البدلات</th>
                                <th>المكافآت</th>
                                <th>الخصومات</th>
                                <th>الصافي</th>
                                <th>الحالة</th>
                                <th>تاريخ الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for salary in salaries %}
                            <tr>
                                <td>
                                    <strong>{{ salary.employee.full_name }}</strong>
                                    <br><small class="text-muted">{{ salary.employee.employee_id }}</small>
                                </td>
                                <td>{{ salary.employee.department.name if salary.employee.department else 'غير محدد' }}</td>
                                <td>{{ "{:,.0f}".format(salary.basic_salary) }}</td>
                                <td>{{ "{:,.0f}".format(salary.allowances) }}</td>
                                <td>{{ "{:,.0f}".format(salary.bonus) }}</td>
                                <td class="text-danger">{{ "{:,.0f}".format(salary.deductions) }}</td>
                                <td class="fw-bold text-success">{{ "{:,.0f}".format(salary.net_salary) }}</td>
                                <td>
                                    {% if salary.status == 'Paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                    {% elif salary.status == 'Pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% else %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ salary.payment_date.strftime('%Y-%m-%d') if salary.payment_date else 'غير محدد' }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <br>لا توجد رواتب مطابقة للفلاتر المحددة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- ملخص إضافي -->
        {% if salaries %}
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> ملخص مالي</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>إجمالي الرواتب الأساسية</h6>
                        <h4 class="text-primary">{{ "{:,.0f}".format(salaries|sum(attribute='basic_salary')) }} ريال</h4>
                    </div>
                    <div class="col-md-4">
                        <h6>إجمالي البدلات والمكافآت</h6>
                        <h4 class="text-success">{{ "{:,.0f}".format(salaries|sum(attribute='allowances') + salaries|sum(attribute='bonus')) }} ريال</h4>
                    </div>
                    <div class="col-md-4">
                        <h6>إجمالي الخصومات</h6>
                        <h4 class="text-danger">{{ "{:,.0f}".format(salaries|sum(attribute='deductions')) }} ريال</h4>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم توزيع الرواتب حسب القسم
        const departmentData = {
            labels: ['تقنية المعلومات', 'الموارد البشرية', 'المالية', 'التسويق', 'العمليات'],
            datasets: [{
                data: [150000, 80000, 120000, 90000, 110000],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8'],
                borderWidth: 2
            }]
        };

        new Chart(document.getElementById('departmentChart'), {
            type: 'doughnut',
            data: departmentData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' }
                }
            }
        });

        // رسم حالات الرواتب
        const statusData = {
            labels: ['مدفوع', 'معلق', 'ملغي'],
            datasets: [{
                data: [{{ stats.paid_count }}, {{ stats.pending_count }}, 2],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 1
            }]
        };

        new Chart(document.getElementById('statusChart'), {
            type: 'bar',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        function exportToExcel() {
            window.location.href = '/export_salary_report_excel?' + new URLSearchParams(window.location.search);
        }

        function exportToPDF() {
            window.location.href = '/export_salary_report_pdf?' + new URLSearchParams(window.location.search);
        }
    </script>
</body>
</html>
